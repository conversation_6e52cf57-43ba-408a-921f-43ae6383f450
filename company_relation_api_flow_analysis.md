# company_relation_api 程序运行流程分析

## 概述

`company_relation_api` 是一个招股说明书相关公司信息提取接口，主要功能是从招股说明书中提取客户、供应商、同行业公司信息。该接口集成了向量检索、大模型分析、数据清洗等多种技术。

## 完整流程图

```mermaid
flowchart TD
    %% 主流程开始
    A["🚀 开始: company_relation<br/>📁 api/routes/company_relation_api.py<br/>🔧 company_relation()<br/>📥 入参: ComInfoEntity{pdf_name:str}<br/>📤 出参: SuccessResponse{code:int, message:str, data:dict} | FalseResponse{code:int, message:str, data:dict}<br/>🔗 调用: common_ext(), pdf_name_to_filename映射"] --> B["📝 参数映射<br/>📁 api/routes/company_relation_api.py<br/>🔧 pdf_name_to_filename.get()<br/>📥 入参: company_name:str<br/>📤 出参: pdf_filename:str<br/>🔗 调用: 无"]
    
    B --> C["🔄 通用提取函数<br/>📁 api/routes/company_relation_api.py<br/>🔧 common_ext()<br/>📥 入参: company_name:str, pdf_filename:str, industry_chain:str='工业机器人'<br/>📤 出参: SuccessResponse{data:dict}<br/>🔗 调用: MongodbUtil.query_docs_by_condition(), CompanyAnalysis()"]
    
    C --> D{"🔍 检查历史数据<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.query_docs_by_condition()<br/>🗄️ 表: COMPANY_RELATION_HISTORY<br/>📥 入参: collection_name:str, search_condition:dict{company_name}<br/>📤 出参: result:list<br/>🔗 调用: MongodbUtil.connect()"}
    
    D -->|有历史数据| E["✅ 返回历史结果<br/>📁 entity/company_relation_entity.py<br/>🔧 SuccessResponse()<br/>📥 入参: data:dict<br/>📤 出参: SuccessResponse{code:200, message:'success', data:dict}<br/>🔗 调用: 无"]
    
    D -->|无历史数据| F["🔧 初始化分析工具<br/>📁 api/routes/company_relation_api.py<br/>🔧 实例化MilvusUtil, TextEmbedService, CompanyAnalysis<br/>📥 入参: 无<br/>📤 出参: milvus_util, embutil, com_analysis<br/>🔗 调用: MilvusUtil(), TextEmbedService(), CompanyAnalysis()"]
    
    %% 供应商分析子流程
    F --> G["🏭 供应商分析<br/>📁 service/company_relation_service.py<br/>🔧 CompanyAnalysis.company_supplier()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: supplier_dict:dict{year:list, is_keyword_hit:bool}<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"]
    
    G --> G1["🔍 供应商向量检索<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: query:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]
    
    G1 --> G2["📚 供应商RAG检索<br/>📁 service/company_relation_service.py<br/>🔧 CompanyAnalysis.common_rag()<br/>🗄️ 表: PROSPECTUS_REPORT_MILVUS<br/>📥 入参: query:str, pdf_filename:str, keywords:list<br/>📤 出参: documents:str, is_hit:bool<br/>🔗 调用: search_by_vector(), reranker.rerank()"]
    
    G2 --> G3["🤖 供应商LLM分析<br/>📁 service/company_relation_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]
    
    G3 --> G4["🧹 供应商数据清洗<br/>📁 api/routes/company_relation_api.py<br/>🔧 special_clean_process()<br/>📥 入参: company_dict:dict<br/>📤 出参: cleaned_dict:dict<br/>🔗 调用: common_clean_process()"]
    
    %% 同行业分析子流程
    G4 --> H["🏢 同行业分析<br/>📁 service/company_relation_service.py<br/>🔧 CompanyAnalysis.company_same_industry()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: same_industry_list:dict{same_industry_list:list, is_keyword_hit:bool}<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"]
    
    H --> H1["🔍 同行业向量检索<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: query:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]
    
    H1 --> H2["📚 同行业RAG检索<br/>📁 service/company_relation_service.py<br/>🔧 CompanyAnalysis.common_rag()<br/>🗄️ 表: PROSPECTUS_REPORT_MILVUS<br/>📥 入参: query:str, pdf_filename:str, keywords:list['同行业上市公司', '同行业企业']<br/>📤 出参: documents:str, is_hit:bool<br/>🔗 调用: search_by_vector(), reranker.rerank()"]
    
    H2 --> H3["🤖 同行业LLM分析<br/>📁 service/company_relation_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]
    
    H3 --> H4["🧹 同行业数据清洗<br/>📁 api/routes/company_relation_api.py<br/>🔧 special_clean_process()<br/>📥 入参: company_dict:dict<br/>📤 出参: cleaned_dict:dict<br/>🔗 调用: common_clean_process()"]
    
    %% 客户分析子流程
    H4 --> I["👥 客户分析<br/>📁 service/company_relation_service.py<br/>🔧 CompanyAnalysis.company_client()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: client_dict:dict{year:list, is_keyword_hit:bool}<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"]
    
    I --> I1["🔍 客户向量检索<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: query:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]
    
    I1 --> I2["📚 客户RAG检索<br/>📁 service/company_relation_service.py<br/>🔧 CompanyAnalysis.common_rag()<br/>🗄️ 表: PROSPECTUS_REPORT_MILVUS<br/>📥 入参: query:str, pdf_filename:str, keywords:list['前%客户']<br/>📤 出参: documents:str, is_hit:bool<br/>🔗 调用: search_by_vector(), reranker.rerank()"]
    
    I2 --> I3["🤖 客户LLM分析<br/>📁 service/company_relation_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]
    
    I3 --> I4["🧹 客户数据清洗<br/>📁 api/routes/company_relation_api.py<br/>🔧 special_clean_process()<br/>📥 入参: company_dict:dict<br/>📤 出参: cleaned_dict:dict<br/>🔗 调用: common_clean_process()"]
    
    %% 文档信息获取
    I4 --> J["📄 获取文档信息<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.query_docs_by_condition()<br/>🗄️ 表: source_notice<br/>📥 入参: collection_name:str, search_condition:dict{source_type, title}<br/>📤 出参: doc_list:list<br/>🔗 调用: MongodbUtil.connect()"]
    
    J --> J1["📋 获取文档详情<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.query_doc_by_id()<br/>🗄️ 表: source_notice<br/>📥 入参: collection_name:str, doc_id:str<br/>📤 出参: doc_info:dict{source_url, document_path}<br/>🔗 调用: MongodbUtil.coll().find_one()"]
    
    %% 数据结构化
    J1 --> K["🏗️ 数据结构化<br/>📁 api/routes/company_relation_api.py<br/>🔧 构建data_list<br/>📥 入参: supplier_dict, same_industry_list, client_dict, file_info<br/>📤 出参: data_list:list[dict{industry_chain, company_name, affiliate, relation_type, year, ranking}]<br/>🔗 调用: 无"]
    
    K --> K1["🆔 生成UUID<br/>📁 api/routes/company_relation_api.py<br/>🔧 uuid.uuid1().hex<br/>📥 入参: 无<br/>📤 出参: unique_id:str<br/>🔗 调用: uuid.uuid1()"]
    
    K1 --> L["💾 保存结果<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.insert_one()<br/>🗄️ 表: COMPANY_RELATION_HISTORY<br/>📥 入参: collection_name:str, doc_content:dict{_id, company_name, 招股书url, 公司数量, 供应商汇总, 同行业公司汇总, 客户汇总, 待入库具体信息}<br/>📤 出参: insert_result<br/>🔗 调用: MongodbUtil.coll().insert_one()"]
    
    L --> M["✅ 返回成功响应<br/>📁 entity/company_relation_entity.py<br/>🔧 SuccessResponse()<br/>📥 入参: data:dict<br/>📤 出参: SuccessResponse{code:200, message:'success', data:dict}<br/>🔗 调用: 无"]
    
    %% 异常处理
    A --> N["⚠️ 异常捕获<br/>📁 api/routes/company_relation_api.py<br/>🔧 try-except块<br/>📥 入参: Exception<br/>📤 出参: error_detail:str<br/>🔗 调用: 无"]
    N --> O["❌ 错误响应<br/>📁 entity/company_relation_entity.py<br/>🔧 FalseResponse()<br/>📥 入参: data:dict{error:str}<br/>📤 出参: FalseResponse{code:500, message:'error', data:dict}<br/>🔗 调用: 无"]
    
    E --> Z[🏁 结束]
    M --> Z
    O --> Z
    
    %% 样式定义
    style A fill:#e1f5fe
    style Z fill:#e8f5e8
    style G fill:#fff3e0
    style H fill:#fff9c4
    style I fill:#f3e5f5
    style N fill:#ffebee
    style O fill:#ffcdd2
```

## 供应商分析详细流程图

```mermaid
flowchart TD
    A["🏭 供应商分析开始<br/>📁 service/company_relation_service.py<br/>🔧 company_supplier()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: supplier_dict:dict{year:list, is_keyword_hit:bool}<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"] --> B["❓ 构建查询问题<br/>📁 service/company_relation_service.py<br/>🔧 构建query字符串<br/>📥 入参: company_name:str<br/>📤 出参: query:str='{company_name}公司的前 名供应商'<br/>🔗 调用: 无"]

    B --> C["🧮 文本向量化<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: texts:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]

    C --> D["📚 RAG检索<br/>📁 service/company_relation_service.py<br/>🔧 CompanyAnalysis.common_rag()<br/>🗄️ 表: PROSPECTUS_REPORT_MILVUS<br/>📥 入参: query:str, pdf_filename:str, keywords:list['前%供应商']<br/>📤 出参: documents:str, is_hit:bool<br/>🔗 调用: search_by_vector(), reranker.rerank()"]

    D --> E["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ 向量库: PROSPECTUS_REPORT_MILVUS<br/>📥 入参: collection_name:str, vector:list[float], limit:int, filter:str<br/>📤 出参: search_results:list[dict]<br/>🔗 调用: milvus_client.search()"]

    E --> F["🔄 重排序<br/>📁 utils/reranker_util.py<br/>🔧 QueryReranker.rerank()<br/>📥 入参: query:str, documents:list, top_k:int<br/>📤 出参: reranked_docs:list<br/>🔗 调用: reranker模型"]

    F --> G["🤖 大模型分析<br/>📁 service/company_relation_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict{role:str, content:str}], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]

    G --> H["📝 解析结果<br/>📁 service/company_relation_service.py<br/>🔧 解析LLM输出<br/>📥 入参: response:str<br/>📤 出参: supplier_dict:dict{year:list[str], is_keyword_hit:bool}<br/>🔗 调用: 字符串处理方法"]

    H --> I["✅ 返回供应商结果<br/>📁 service/company_relation_service.py<br/>🔧 return supplier_dict<br/>📥 入参: 完整数据:dict<br/>📤 出参: supplier_dict:dict{year:list, is_keyword_hit:bool}<br/>🔗 调用: 无"]

    style A fill:#e1f5fe
    style D fill:#fff9c4
    style E fill:#e8f5e8
    style G fill:#f3e5f5
    style I fill:#e8f5e8
```

## 同行业分析详细流程图

```mermaid
flowchart TD
    A["🏢 同行业分析开始<br/>📁 service/company_relation_service.py<br/>🔧 company_same_industry()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: same_industry_dict:dict{same_industry_list:list, is_keyword_hit:bool}<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"] --> B["❓ 构建查询问题<br/>📁 service/company_relation_service.py<br/>🔧 构建query字符串<br/>📥 入参: company_name:str<br/>📤 出参: query:str='{company_name}公司的同行业上市公司名单'<br/>🔗 调用: 无"]

    B --> C["🧮 文本向量化<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: texts:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]

    C --> D["📚 RAG检索<br/>📁 service/company_relation_service.py<br/>🔧 CompanyAnalysis.common_rag()<br/>🗄️ 表: PROSPECTUS_REPORT_MILVUS<br/>📥 入参: query:str, pdf_filename:str, keywords:list['同行业上市公司', '同行业企业', '同行业相关企业']<br/>📤 出参: documents:str, is_hit:bool<br/>🔗 调用: search_by_vector(), reranker.rerank()"]

    D --> E["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ 向量库: PROSPECTUS_REPORT_MILVUS<br/>📥 入参: collection_name:str, vector:list[float], limit:int, filter:str<br/>📤 出参: search_results:list[dict]<br/>🔗 调用: milvus_client.search()"]

    E --> F["🔄 重排序<br/>📁 utils/reranker_util.py<br/>🔧 QueryReranker.rerank()<br/>📥 入参: query:str, documents:list, top_k:int<br/>📤 出参: reranked_docs:list<br/>🔗 调用: reranker模型"]

    F --> G["🤖 大模型分析<br/>📁 service/company_relation_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict{role:str, content:str}], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]

    G --> H["📝 解析结果<br/>📁 service/company_relation_service.py<br/>🔧 解析LLM输出<br/>📥 入参: response:str<br/>📤 出参: same_industry_dict:dict{same_industry_list:list[str], is_keyword_hit:bool}<br/>🔗 调用: 字符串处理方法"]

    H --> I["✅ 返回同行业结果<br/>📁 service/company_relation_service.py<br/>🔧 return same_industry_dict<br/>📥 入参: 完整数据:dict<br/>📤 出参: same_industry_dict:dict{same_industry_list:list, is_keyword_hit:bool}<br/>🔗 调用: 无"]

    style A fill:#e1f5fe
    style D fill:#fff9c4
    style E fill:#e8f5e8
    style G fill:#f3e5f5
    style I fill:#e8f5e8
```

## 客户分析详细流程图

```mermaid
flowchart TD
    A["👥 客户分析开始<br/>📁 service/company_relation_service.py<br/>🔧 company_client()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: client_dict:dict{year:list, is_keyword_hit:bool}<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"] --> B["❓ 构建查询问题<br/>📁 service/company_relation_service.py<br/>🔧 构建query字符串<br/>📥 入参: company_name:str<br/>📤 出参: query:str='{company_name}公司报告期 主要销售客户的名称'<br/>🔗 调用: 无"]

    B --> C["🧮 文本向量化<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: texts:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]

    C --> D["📚 RAG检索<br/>📁 service/company_relation_service.py<br/>🔧 CompanyAnalysis.common_rag()<br/>🗄️ 表: PROSPECTUS_REPORT_MILVUS<br/>📥 入参: query:str, pdf_filename:str, keywords:list['前%客户']<br/>📤 出参: documents:str, is_hit:bool<br/>🔗 调用: search_by_vector(), reranker.rerank()"]

    D --> E["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ 向量库: PROSPECTUS_REPORT_MILVUS<br/>📥 入参: collection_name:str, vector:list[float], limit:int, filter:str<br/>📤 出参: search_results:list[dict]<br/>🔗 调用: milvus_client.search()"]

    E --> F["🔄 重排序<br/>📁 utils/reranker_util.py<br/>🔧 QueryReranker.rerank()<br/>📥 入参: query:str, documents:list, top_k:int<br/>📤 出参: reranked_docs:list<br/>🔗 调用: reranker模型"]

    F --> G["🤖 大模型分析<br/>📁 service/company_relation_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict{role:str, content:str}], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]

    G --> H["📝 解析结果<br/>📁 service/company_relation_service.py<br/>🔧 解析LLM输出<br/>📥 入参: response:str<br/>📤 出参: client_dict:dict{year:list[str], is_keyword_hit:bool}<br/>🔗 调用: 字符串处理方法"]

    H --> I["✅ 返回客户结果<br/>📁 service/company_relation_service.py<br/>🔧 return client_dict<br/>📥 入参: 完整数据:dict<br/>📤 出参: client_dict:dict{year:list, is_keyword_hit:bool}<br/>🔗 调用: 无"]

    style A fill:#e1f5fe
    style D fill:#fff9c4
    style E fill:#e8f5e8
    style G fill:#f3e5f5
    style I fill:#e8f5e8
```

## 核心接口详细说明

### 主要文件和接口调用关系

#### 1. **api/routes/company_relation_api.py**

**company_relation 主接口**
- **调用的其他接口**:
  - `pdf_name_to_filename.get()` - 文件名映射
  - `common_ext()` - 通用提取函数
- **入参**: `ComInfoEntity{pdf_name:str}` - 从"机器人","东方国信","三环集团","晶瑞股份","先导股份"中选择
- **出参**: `SuccessResponse{code:200, message:'success', data:dict}` 或 `FalseResponse{code:500, message:'error', data:dict}`
- **查询表**: `COMPANY_RELATION_HISTORY`, `source_notice`, `PROSPECTUS_REPORT_MILVUS`

**common_ext 通用提取函数**
- **调用的其他接口**:
  - `MongodbUtil.query_docs_by_condition()` - 历史数据查询
  - `CompanyAnalysis()` - 公司分析服务
  - `special_clean_process()` - 数据清洗
  - `MongodbUtil.insert_one()` - 结果保存
- **入参**: `{company_name:str, pdf_filename:str, industry_chain:str='工业机器人'}`
- **出参**: `SuccessResponse{data:dict}`
- **查询表**: `COMPANY_RELATION_HISTORY`, `source_notice`

#### 2. **service/company_relation_service.py**

**CompanyAnalysis.company_supplier 供应商分析**
- **调用的其他接口**:
  - `TextEmbedService.text_embedding()` - 文本向量化
  - `common_rag()` - RAG检索
  - `Llm_Service.answer_question()` - 大模型问答
- **入参**: `{company_name:str, pdf_filename:str}`
- **出参**: `{year:list[str], is_keyword_hit:bool}`
- **查询表**: `PROSPECTUS_REPORT_MILVUS` (Milvus向量库)

**CompanyAnalysis.company_same_industry 同行业分析**
- **调用的其他接口**:
  - `TextEmbedService.text_embedding()` - 文本向量化
  - `common_rag()` - RAG检索
  - `Llm_Service.answer_question()` - 大模型问答
- **入参**: `{company_name:str, pdf_filename:str}`
- **出参**: `{same_industry_list:list[str], is_keyword_hit:bool}`
- **查询表**: `PROSPECTUS_REPORT_MILVUS` (Milvus向量库)

**CompanyAnalysis.company_client 客户分析**
- **调用的其他接口**:
  - `TextEmbedService.text_embedding()` - 文本向量化
  - `common_rag()` - RAG检索
  - `Llm_Service.answer_question()` - 大模型问答
- **入参**: `{company_name:str, pdf_filename:str}`
- **出参**: `{year:list[str], is_keyword_hit:bool}`
- **查询表**: `PROSPECTUS_REPORT_MILVUS` (Milvus向量库)

**CompanyAnalysis.common_rag RAG检索**
- **调用的其他接口**:
  - `MilvusUtil.search_by_vector()` - 向量检索
  - `QueryReranker.rerank()` - 重排序
- **入参**: `{query:str, pdf_filename:str, keywords:list[str]}`
- **出参**: `{documents:str, is_hit:bool}`
- **查询表**: `PROSPECTUS_REPORT_MILVUS` (Milvus向量库)

## 数据表操作详细信息

### MongoDB 集合操作

| 集合名称 | 操作类型 | 接口方法 | 查询条件 | 数据结构 | 用途说明 |
|---------|---------|----------|----------|----------|----------|
| COMPANY_RELATION_HISTORY | 查询 | query_docs_by_condition() | `{company_name:str}` | 历史分析结果 | 缓存查询，避免重复分析 |
| COMPANY_RELATION_HISTORY | 插入 | insert_one() | 无 | 分析结果数据 | 保存分析结果 |
| source_notice | 查询 | query_docs_by_condition() | `{source_type, title}` | 文档信息 | 获取招股说明书文档信息 |

### Milvus 向量库操作

| 集合名称 | 操作类型 | 接口方法 | 查询条件 | 数据结构 | 用途说明 |
|---------|---------|----------|----------|----------|----------|
| PROSPECTUS_REPORT_MILVUS | 向量检索 | search_by_vector() | `{vector, limit_top_k, filter}` | 招股说明书向量 | 语义相似度检索 |

## 关键功能模块详细说明

### 1. 历史数据缓存机制
- **缓存键**: 基于公司名称 `company_name`
- **缓存存储**: MongoDB `COMPANY_RELATION_HISTORY` 集合
- **缓存查询**: `MongodbUtil.query_docs_by_condition()` 方法
- **缓存数据结构**:
```python
{
    "_id": str,                      # 唯一标识
    "company_name": str,             # 公司名称
    "招股书url": str,                # 招股说明书URL
    "公司数量": int,                 # 提取的公司总数
    "供应商汇总": dict,              # 供应商汇总信息
    "同行业公司汇总": dict,          # 同行业公司汇总
    "客户汇总": dict,                # 客户汇总信息
    "待入库具体信息": list           # 详细的公司关系信息
}
```

### 2. 向量检索和RAG功能
- **检索流程**: 问题向量化 → Milvus检索 → 重排序 → 文档整合
- **关键词匹配**: 支持关键词命中检测，提高检索准确性
- **重排序**: 使用QueryReranker对检索结果进行重排序
- **文档过滤**: 基于文件名进行精确过滤

### 3. 大模型分析策略
- **供应商分析**: 提取前N名供应商信息，按年份组织
- **同行业分析**: 识别同行业上市公司和相关企业
- **客户分析**: 提取主要销售客户信息
- **输出格式**: 统一的管道分隔格式，便于后续处理

### 4. 数据清洗和标准化
- **special_clean_process**: 专门的清洗函数
- **common_clean_process**: 通用清洗规则
- **数据验证**: 检查提取结果的有效性
- **格式标准化**: 统一数据格式和结构

## 技术特点

1. **预定义文件映射**: 支持从预定义的公司名称映射到对应的招股说明书文件
2. **RAG增强检索**: 结合向量检索和关键词匹配，提高检索准确性
3. **多维度分析**: 供应商、客户、同行业三个维度的全面分析
4. **智能缓存**: 基于公司名称的缓存机制，避免重复计算
5. **数据清洗**: 完善的数据清洗和验证流程，使用special_clean_process进行数据过滤
6. **结构化输出**: 标准化的数据结构和格式
7. **重排序优化**: 使用重排序模型提高检索结果质量
8. **文档信息集成**: 自动查询和集成source_notice表中的文档信息
9. **异常处理**: 完善的错误处理和日志记录
10. **灵活配置**: 支持自定义PDF文件名和产业链参数
