# company_relation_api 详细流程分析

## 概述

`company_relation_api` 是一个招股说明书相关公司信息提取接口，集成了向量检索、大模型分析、数据清洗等多种技术。本文档详细展示了接口调用关系、入参出参、数据表操作等信息。

## 1. 主流程图

```mermaid
flowchart TD
    A["🚀 开始: company_relation<br/>📁 api/routes/company_relation_api.py<br/>🔧 company_relation()<br/>📥 入参: ComInfoEntity{pdf_name:str}<br/>📤 出参: SuccessResponse{code:int, message:str, data:dict} | FalseResponse{code:int, message:str, data:dict}<br/>🔗 调用: common_ext(), pdf_name_to_filename映射"] --> B["📝 参数映射<br/>📁 api/routes/company_relation_api.py<br/>🔧 pdf_name_to_filename.get()<br/>📥 入参: company_name:str<br/>📤 出参: pdf_filename:str<br/>🔗 调用: 无"]
    
    B --> C["🔄 通用提取函数<br/>📁 api/routes/company_relation_api.py<br/>🔧 common_ext()<br/>📥 入参: company_name:str, pdf_filename:str, industry_chain:str='工业机器人'<br/>📤 出参: SuccessResponse{data:dict}<br/>🔗 调用: MongodbUtil.query_docs_by_condition(), CompanyAnalysis()"]
    
    C --> D{"🔍 检查历史数据<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.query_docs_by_condition()<br/>🗄️ 表: COMPANY_RELATION_HISTORY<br/>📥 入参: collection_name:str, search_condition:dict{company_name}<br/>📤 出参: result:list<br/>🔗 调用: MongodbUtil.connect()"}
    
    D -->|有历史数据| E["✅ 返回历史结果<br/>📁 entity/company_relation_entity.py<br/>🔧 SuccessResponse()<br/>📥 入参: data:dict<br/>📤 出参: SuccessResponse{code:200, message:'success', data:dict}<br/>🔗 调用: 无"]
    
    D -->|无历史数据| F["🔧 初始化分析工具<br/>📁 api/routes/company_relation_api.py<br/>🔧 实例化分析工具<br/>📥 入参: 无<br/>📤 出参: milvus_util, embutil, com_analysis<br/>🔗 调用: MilvusUtil(), TextEmbedService(), CompanyAnalysis()"]
    
    F --> G["🏭 供应商分析<br/>📁 service/company_relation_service.py<br/>🔧 CompanyAnalysis.company_supplier()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: supplier_dict:dict{year:list, is_keyword_hit:bool}<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"]
    
    G --> H["🏢 同行业分析<br/>📁 service/company_relation_service.py<br/>🔧 CompanyAnalysis.company_same_industry()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: same_industry_list:dict{same_industry_list:list, is_keyword_hit:bool}<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"]
    
    H --> I["👥 客户分析<br/>📁 service/company_relation_service.py<br/>🔧 CompanyAnalysis.company_client()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: client_dict:dict{year:list, is_keyword_hit:bool}<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"]
    
    I --> J["💾 保存结果<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.insert_one()<br/>🗄️ 表: COMPANY_RELATION_HISTORY<br/>📥 入参: collection_name:str, doc_content:dict<br/>📤 出参: insert_result<br/>🔗 调用: MongodbUtil.coll().insert_one()"]
    
    J --> K["✅ 返回成功响应<br/>📁 entity/company_relation_entity.py<br/>🔧 SuccessResponse()<br/>📥 入参: data:dict<br/>📤 出参: SuccessResponse{code:200, message:'success', data:dict}<br/>🔗 调用: 无"]
    
    %% 异常处理
    A --> L["⚠️ 异常捕获<br/>📁 api/routes/company_relation_api.py<br/>🔧 try-except块<br/>📥 入参: Exception<br/>📤 出参: error_detail:str<br/>🔗 调用: 无"]
    L --> M["❌ 错误响应<br/>📁 entity/company_relation_entity.py<br/>🔧 FalseResponse()<br/>📥 入参: data:dict{error:str}<br/>📤 出参: FalseResponse{code:500, message:'error', data:dict}<br/>🔗 调用: 无"]
    
    E --> Z[🏁 结束]
    K --> Z
    M --> Z
    
    style A fill:#e1f5fe
    style Z fill:#e8f5e8
    style G fill:#fff3e0
    style H fill:#fff9c4
    style I fill:#f3e5f5
    style L fill:#ffebee
    style M fill:#ffcdd2
```

## 2. 供应商分析子流程图

```mermaid
flowchart TD
    A["🏭 供应商分析开始<br/>📁 service/company_relation_service.py<br/>🔧 company_supplier()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: supplier_dict:dict{year:list, is_keyword_hit:bool}<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"] --> B["❓ 构建查询问题<br/>📁 service/company_relation_service.py<br/>🔧 构建query字符串<br/>📥 入参: company_name:str<br/>📤 出参: query:str='{company_name}公司的前 名供应商'<br/>🔗 调用: 无"]
    
    B --> C["🧮 文本向量化<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: texts:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]
    
    C --> D["📚 RAG检索<br/>📁 service/company_relation_service.py<br/>🔧 CompanyAnalysis.common_rag()<br/>🗄️ 表: PROSPECTUS_REPORT_MILVUS<br/>📥 入参: query:str, pdf_filename:str, keywords:list['前%供应商']<br/>📤 出参: documents:str, is_hit:bool<br/>🔗 调用: search_by_vector(), reranker.rerank()"]
    
    D --> E["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ 向量库: PROSPECTUS_REPORT_MILVUS<br/>📥 入参: collection_name:str, vector:list[float], limit:int, filter:str<br/>📤 出参: search_results:list[dict]<br/>🔗 调用: milvus_client.search()"]
    
    E --> F["🔄 重排序<br/>📁 utils/reranker_util.py<br/>🔧 QueryReranker.rerank()<br/>📥 入参: query:str, documents:list, top_k:int<br/>📤 出参: reranked_docs:list<br/>🔗 调用: reranker模型"]
    
    F --> G["🤖 大模型分析<br/>📁 service/company_relation_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict{role:str, content:str}], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]
    
    G --> H["📝 解析结果<br/>📁 service/company_relation_service.py<br/>🔧 解析LLM输出<br/>📥 入参: response:str<br/>📤 出参: supplier_dict:dict{year:list[str], is_keyword_hit:bool}<br/>🔗 调用: 字符串处理方法"]
    
    H --> I["✅ 返回供应商结果<br/>📁 service/company_relation_service.py<br/>🔧 return supplier_dict<br/>📥 入参: 完整数据:dict<br/>📤 出参: supplier_dict:dict{year:list, is_keyword_hit:bool}<br/>🔗 调用: 无"]
    
    style A fill:#e1f5fe
    style D fill:#fff9c4
    style E fill:#e8f5e8
    style G fill:#f3e5f5
    style I fill:#e8f5e8
```

## 3. 同行业分析子流程图

```mermaid
flowchart TD
    A["🏢 同行业分析开始<br/>📁 service/company_relation_service.py<br/>🔧 company_same_industry()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: same_industry_dict:dict{same_industry_list:list, is_keyword_hit:bool}<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"] --> B["❓ 构建查询问题<br/>📁 service/company_relation_service.py<br/>🔧 构建query字符串<br/>📥 入参: company_name:str<br/>📤 出参: query:str='{company_name}公司的同行业上市公司名单'<br/>🔗 调用: 无"]
    
    B --> C["🧮 文本向量化<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: texts:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]
    
    C --> D["📚 RAG检索<br/>📁 service/company_relation_service.py<br/>🔧 CompanyAnalysis.common_rag()<br/>🗄️ 表: PROSPECTUS_REPORT_MILVUS<br/>📥 入参: query:str, pdf_filename:str, keywords:list['同行业上市公司', '同行业企业', '同行业相关企业']<br/>📤 出参: documents:str, is_hit:bool<br/>🔗 调用: search_by_vector(), reranker.rerank()"]
    
    D --> E["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ 向量库: PROSPECTUS_REPORT_MILVUS<br/>📥 入参: collection_name:str, vector:list[float], limit:int, filter:str<br/>📤 出参: search_results:list[dict]<br/>🔗 调用: milvus_client.search()"]
    
    E --> F["🔄 重排序<br/>📁 utils/reranker_util.py<br/>🔧 QueryReranker.rerank()<br/>📥 入参: query:str, documents:list, top_k:int<br/>📤 出参: reranked_docs:list<br/>🔗 调用: reranker模型"]
    
    F --> G["🤖 大模型分析<br/>📁 service/company_relation_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict{role:str, content:str}], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]
    
    G --> H["📝 解析结果<br/>📁 service/company_relation_service.py<br/>🔧 解析LLM输出<br/>📥 入参: response:str<br/>📤 出参: same_industry_dict:dict{same_industry_list:list[str], is_keyword_hit:bool}<br/>🔗 调用: 字符串处理方法"]
    
    H --> I["✅ 返回同行业结果<br/>📁 service/company_relation_service.py<br/>🔧 return same_industry_dict<br/>📥 入参: 完整数据:dict<br/>📤 出参: same_industry_dict:dict{same_industry_list:list, is_keyword_hit:bool}<br/>🔗 调用: 无"]
    
    style A fill:#e1f5fe
    style D fill:#fff9c4
    style E fill:#e8f5e8
    style G fill:#f3e5f5
    style I fill:#e8f5e8
```

## 4. 客户分析子流程图

```mermaid
flowchart TD
    A["👥 客户分析开始<br/>📁 service/company_relation_service.py<br/>🔧 company_client()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: client_dict:dict{year:list, is_keyword_hit:bool}<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"] --> B["❓ 构建查询问题<br/>📁 service/company_relation_service.py<br/>🔧 构建query字符串<br/>📥 入参: company_name:str<br/>📤 出参: query:str='{company_name}公司报告期 主要销售客户的名称'<br/>🔗 调用: 无"]
    
    B --> C["🧮 文本向量化<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: texts:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]
    
    C --> D["📚 RAG检索<br/>📁 service/company_relation_service.py<br/>🔧 CompanyAnalysis.common_rag()<br/>🗄️ 表: PROSPECTUS_REPORT_MILVUS<br/>📥 入参: query:str, pdf_filename:str, keywords:list['前%客户']<br/>📤 出参: documents:str, is_hit:bool<br/>🔗 调用: search_by_vector(), reranker.rerank()"]
    
    D --> E["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ 向量库: PROSPECTUS_REPORT_MILVUS<br/>📥 入参: collection_name:str, vector:list[float], limit:int, filter:str<br/>📤 出参: search_results:list[dict]<br/>🔗 调用: milvus_client.search()"]
    
    E --> F["🔄 重排序<br/>📁 utils/reranker_util.py<br/>🔧 QueryReranker.rerank()<br/>📥 入参: query:str, documents:list, top_k:int<br/>📤 出参: reranked_docs:list<br/>🔗 调用: reranker模型"]
    
    F --> G["🤖 大模型分析<br/>📁 service/company_relation_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict{role:str, content:str}], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]
    
    G --> H["📝 解析结果<br/>📁 service/company_relation_service.py<br/>🔧 解析LLM输出<br/>📥 入参: response:str<br/>📤 出参: client_dict:dict{year:list[str], is_keyword_hit:bool}<br/>🔗 调用: 字符串处理方法"]
    
    H --> I["✅ 返回客户结果<br/>📁 service/company_relation_service.py<br/>🔧 return client_dict<br/>📥 入参: 完整数据:dict<br/>📤 出参: client_dict:dict{year:list, is_keyword_hit:bool}<br/>🔗 调用: 无"]
    
    style A fill:#e1f5fe
    style D fill:#fff9c4
    style E fill:#e8f5e8
    style G fill:#f3e5f5
    style I fill:#e8f5e8
```

## 5. 数据清洗和结构化流程图

```mermaid
flowchart TD
    A["🧹 数据清洗开始<br/>📁 api/routes/company_relation_api.py<br/>🔧 special_clean_process()<br/>📥 入参: supplier_dict:dict, same_industry_list:dict, client_dict:dict<br/>📤 出参: cleaned_data:dict<br/>🔗 调用: common_clean_process()"] --> B["🔧 供应商数据清洗<br/>📁 api/routes/company_relation_api.py<br/>🔧 common_clean_process()<br/>📥 入参: supplier_dict:dict<br/>📤 出参: cleaned_supplier:dict<br/>🔗 调用: 字符串处理方法"]

    B --> C["🔧 同行业数据清洗<br/>📁 api/routes/company_relation_api.py<br/>🔧 common_clean_process()<br/>📥 入参: same_industry_list:dict<br/>📤 出参: cleaned_same_industry:dict<br/>🔗 调用: 字符串处理方法"]

    C --> D["🔧 客户数据清洗<br/>📁 api/routes/company_relation_api.py<br/>🔧 common_clean_process()<br/>📥 入参: client_dict:dict<br/>📤 出参: cleaned_client:dict<br/>🔗 调用: 字符串处理方法"]

    D --> E["📄 获取文档信息<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.query_docs_by_condition()<br/>🗄️ 表: source_notice<br/>📥 入参: collection_name:str, search_condition:dict{source_type:'招股说明书', title:pdf_filename}<br/>📤 出参: doc_list:list<br/>🔗 调用: MongodbUtil.connect()"]

    E --> F["📋 获取文档详情<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.query_doc_by_id()<br/>🗄️ 表: source_notice<br/>📥 入参: collection_name:str, doc_id:str<br/>📤 出参: doc_info:dict{source_url, document_path}<br/>🔗 调用: MongodbUtil.coll().find_one()"]

    F --> G["🏗️ 数据结构化<br/>📁 api/routes/company_relation_api.py<br/>🔧 构建data_list<br/>📥 入参: cleaned_data:dict, file_info:dict<br/>📤 出参: data_list:list[dict{industry_chain, company_name, affiliate, relation_type, year, ranking}]<br/>🔗 调用: 无"]

    G --> H["🆔 生成UUID<br/>📁 api/routes/company_relation_api.py<br/>🔧 uuid.uuid1().hex<br/>📥 入参: 无<br/>📤 出参: unique_id:str<br/>🔗 调用: uuid.uuid1()"]

    H --> I["📊 构建保存数据<br/>📁 api/routes/company_relation_api.py<br/>🔧 构建save_data<br/>📥 入参: 所有处理结果:dict<br/>📤 出参: save_data:dict{_id, company_name, 招股书url, 公司数量, 供应商汇总, 同行业公司汇总, 客户汇总, 待入库具体信息}<br/>🔗 调用: 无"]

    I --> J["✅ 返回清洗结果<br/>📁 api/routes/company_relation_api.py<br/>🔧 return save_data<br/>📥 入参: 完整数据:dict<br/>📤 出参: save_data:dict<br/>🔗 调用: 无"]

    style A fill:#e1f5fe
    style E fill:#e3f2fd
    style F fill:#e3f2fd
    style G fill:#fff3e0
    style J fill:#e8f5e8
```

## 6. 详细接口调用信息

### 6.1 company_relation 主接口
**文件位置**: `api/routes/company_relation_api.py`
**方法名**: `company_relation()`

**调用的其他接口**:
1. `pdf_name_to_filename.get()` - 文件名映射
2. `common_ext()` - 通用提取函数

**详细入参**:
```python
{
    "pdf_name": str             # 公司名称，如"埃夫特"、"拓斯达"等
}
```

**详细出参**:
```python
# 成功响应 (SuccessResponse)
{
    "code": int,                # 响应状态码，200表示成功
    "message": str,             # 响应消息
    "data": {                   # 公司关系分析结果
        "_id": str,             # 唯一标识
        "company_name": str,    # 公司名称
        "招股书url": str,       # 招股说明书URL
        "公司数量": int,        # 提取的公司总数
        "供应商汇总": dict,     # 供应商汇总信息
        "同行业公司汇总": dict, # 同行业公司汇总
        "客户汇总": dict,       # 客户汇总信息
        "待入库具体信息": list  # 详细的公司关系信息
    }
}
```

**查询的数据表**:
1. `COMPANY_RELATION_HISTORY` - 历史数据查询和结果保存
2. `source_notice` - 文档信息查询
3. `PROSPECTUS_REPORT_MILVUS` - 向量检索

### 6.2 common_rag 接口
**文件位置**: `service/company_relation_service.py`
**方法名**: `CompanyAnalysis.common_rag()`

**调用的其他接口**:
1. `MilvusUtil.search_by_vector()` - 向量检索
2. `QueryReranker.rerank()` - 重排序

**详细入参**:
```python
{
    "query": str,               # 检索问题
    "pdf_filename": str,        # PDF文件名
    "keywords": list[str]       # 关键词列表
}
```

**详细出参**:
```python
{
    "documents": str,           # 检索到的文档内容
    "is_hit": bool             # 是否命中关键词
}
```

**查询的数据表**:
1. `PROSPECTUS_REPORT_MILVUS` - Milvus向量数据库

## 7. 核心数据结构

### 7.1 请求参数结构 (ComInfoEntity)
```json
{
  "pdf_name": "公司名称，如埃夫特、拓斯达等"
}
```

### 7.2 供应商分析结果结构
```json
{
  "year": ["供应商A", "供应商B", "供应商C"],
  "is_keyword_hit": true
}
```

### 7.3 同行业分析结果结构
```json
{
  "same_industry_list": ["同行业公司A", "同行业公司B"],
  "is_keyword_hit": true
}
```

### 7.4 客户分析结果结构
```json
{
  "year": ["客户A", "客户B", "客户C"],
  "is_keyword_hit": true
}
```

### 7.5 最终输出结构
```json
{
  "_id": "唯一标识",
  "company_name": "公司名称",
  "招股书url": "招股说明书URL",
  "公司数量": 15,
  "供应商汇总": {
    "供应商数量": 5,
    "供应商列表": ["供应商A", "供应商B"]
  },
  "同行业公司汇总": {
    "同行业公司数量": 8,
    "同行业公司列表": ["同行业公司A", "同行业公司B"]
  },
  "客户汇总": {
    "客户数量": 2,
    "客户列表": ["客户A", "客户B"]
  },
  "待入库具体信息": [
    {
      "industry_chain": "工业机器人",
      "company_name": "目标公司",
      "affiliate": "关联公司",
      "relation_type": "供应商|客户|同行业",
      "year": "年份",
      "ranking": "排名"
    }
  ]
}
```

## 8. 数据表操作汇总

| 操作类型 | 表名 | 操作方法 | 用途 | 入参 | 出参 |
|---------|------|----------|------|------|------|
| 查询 | COMPANY_RELATION_HISTORY | query_docs_by_condition() | 历史数据查询 | search_condition | result_list |
| 插入 | COMPANY_RELATION_HISTORY | insert_one() | 结果保存 | doc_content | insert_result |
| 查询 | source_notice | query_docs_by_condition() | 文档信息查询 | search_condition | doc_list |
| 查询 | source_notice | query_doc_by_id() | 文档详情查询 | doc_id | doc_info |
| 检索 | PROSPECTUS_REPORT_MILVUS | search_by_vector() | 向量检索 | vector, limit_top_k | search_results |

## 9. 技术特点

1. **RAG增强检索**: 结合向量检索和关键词匹配，提高检索准确性
2. **多维度分析**: 供应商、客户、同行业三个维度的全面分析
3. **智能缓存**: 基于公司名称的缓存机制，避免重复计算
4. **数据清洗**: 完善的数据清洗和验证流程
5. **结构化输出**: 标准化的数据结构和格式
6. **异常处理**: 完善的错误处理和日志记录
7. **重排序优化**: 使用重排序模型提高检索结果质量
