# label_merge_perform 程序运行流程分析

## 概述

`label_merge_perform` 是一个标签融合接口，主要功能是对标签提取结果进行智能合并和去重处理。该接口集成了大模型请求、规则合并、MongoDB 数据存储、<PERSON><PERSON><PERSON><PERSON> 向量检索等多种技术。

## 完整流程图

```mermaid
flowchart TD
    %% 主流程开始
    A["🚀 开始: label_merge_perform<br/>📁 api/routes/label_merge.py<br/>🔧 label_merge_perform()<br/>📥 入参: LabelMergeRequest{mongodb_id:str, model:str, prompt:str, merge_type:str, is_ai_extend:bool, is_cached:bool}<br/>📤 出参: SuccessResponse{code:int, message:str, data:dict} | FalseResponse{code:int, message:str, data:dict}<br/>🔗 调用: LogUtil.log_json(), MongodbUtil.coll().find_one(), ai_extend_perform(), perform_common_merge_helper()"] --> B["📝 记录请求参数日志<br/>📁 utils/log_util.py<br/>🔧 LogUtil.log_json()<br/>📥 入参: describe:str='请求参数', kwargs:dict<br/>📤 出参: None<br/>🔗 调用: 无"]

    B --> C{"🔍 检查缓存数据<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.coll().find_one()<br/>🗄️ 表: LABEL_MERGE_PERFORM<br/>📥 入参: filter:dict{raw_label_merge_data_id, model, prompt, merge_type, is_ai_extend, is_cached}<br/>📤 出参: cached_data:dict | None<br/>🔗 调用: MongodbUtil.coll()"}

    C -->|有缓存| D["✅ 返回缓存结果<br/>📁 entity/response_entity.py<br/>🔧 SuccessResponse()<br/>📥 入参: data:dict<br/>📤 出参: SuccessResponse{code:200, message:'success', data:dict}<br/>🔗 调用: 无"]

    C -->|无缓存| E["📊 从MongoDB获取历史数据<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.query_doc_by_id()<br/>🗄️ 表: LABEL_EXTRACT_PERFORM_HISTORY<br/>📥 入参: collection_name:str, doc_id:str<br/>📤 出参: history:dict{chain_structure, node_companies, product, key_companies}<br/>🔗 调用: MongodbUtil.coll().find_one()"]

    E --> F{"🤖 检查is_ai_extend参数<br/>📁 api/routes/label_merge.py<br/>🔧 request.is_ai_extend判断<br/>📥 入参: is_ai_extend:bool, chain_structure:dict<br/>📤 出参: need_ai_extend:bool<br/>🔗 调用: 无"}

    F -->|True且chain_structure为空| G["🧠 执行AI扩展<br/>📁 service/label_merge_service.py<br/>🔧 LabelMergeService.ai_extend_perform()<br/>📥 入参: label_extract_info:dict{product:list, key_companies:list}<br/>📤 出参: chain_structure:dict, mongodb_id_node:dict, mongodb_id_info:dict<br/>🔗 调用: ai_extend_pair(), merge_and_deduplicate()"]

    F -->|False或已有结构| H["⏭️ 跳过AI扩展<br/>📁 api/routes/label_merge.py<br/>🔧 直接进入合并流程<br/>📥 入参: 现有数据结构:dict<br/>📤 出参: 无变化<br/>🔗 调用: 无"]

    %% AI扩展子流程
    G --> G1["🔧 初始化变量<br/>📁 service/label_merge_service.py<br/>🔧 chain_structure = {}<br/>📥 入参: None<br/>📤 出参: chain_structure:dict, mongodb_id_node:dict, mongodb_id_info:dict<br/>🔗 调用: 无"]

    G1 --> G2["🔄 遍历product数组<br/>📁 service/label_merge_service.py<br/>🔧 for item in product<br/>📥 入参: product:list[dict{product_abb:str, company_abb:str}]<br/>📤 出参: item:dict{product_abb:str, company_abb:str}<br/>🔗 调用: 无"]

    G2 --> G3["🤝 AI扩展配对<br/>📁 service/label_merge_service.py<br/>🔧 LabelMergeService.ai_extend_pair()<br/>📥 入参: product:str, company:str<br/>📤 出参: generate_key:str, source_list:list<br/>🔗 调用: search_knowledge_by_question(), answer_question(), get_json_from_text()"]

    G3 --> G4["❓ 构建检索问题<br/>📁 service/label_merge_service.py<br/>🔧 构建question字符串<br/>📥 入参: product:str, company:str<br/>📤 出参: question:str='请在{company}中的{product} 生成对应的产业链中的位置'<br/>🔗 调用: 无"]

    %% 向量检索子流程
    G4 --> V1["🔍 知识库检索<br/>📁 service/kb_service.py<br/>🔧 KbService.search_knowledge_by_question()<br/>🗄️ 表: KNOWLEDGE_REPORT_ALL<br/>📥 入参: collection_name:str, question:str, limit_top_k:int=3<br/>📤 出参: doc_list:list[dict{mongodb_id, file_source, file_title, file_url, chunk_content}]<br/>🔗 调用: text_embedding(), search_by_vector()"]

    V1 --> V2["🧮 文本向量化<br/>📁 service/text_embed_service.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: texts:list[str]<br/>📤 出参: embed_vector:list[float]<br/>🔗 调用: 向量化模型API"]

    V2 --> V3["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ 向量库: Milvus KNOWLEDGE_REPORT_ALL<br/>📥 入参: collection_name:str, vector:list[float], limit_top_k:int<br/>📤 出参: search_results:list[dict{id, distance, entity}]<br/>🔗 调用: milvus_client.search()"]

    V3 --> V4["📚 获取Top-K文档<br/>📁 service/kb_service.py<br/>🔧 返回相关文档列表<br/>📥 入参: search_results:list<br/>📤 出参: doc_list:list[dict]<br/>🔗 调用: 无"]

    V4 --> G5["📚 组织知识库内容<br/>📁 service/label_merge_service.py<br/>🔧 knowledge = '\\n\\n'.join(content_list)<br/>📥 入参: content_list:list[str]<br/>📤 出参: knowledge:str<br/>🔗 调用: 无"]

    %% AI扩展大模型调用
    G5 --> G6["💭 构建大模型提示词<br/>📁 configs/prompt_config.py<br/>🔧 PromptConfig.LABEL_MERGE_AI_EXTEND_SYSTEM_PROMPT.format()<br/>📥 入参: knowledge:str, product:str, company:str<br/>📤 出参: query:str<br/>🔗 调用: 无"]

    G6 --> G7["🤖 大模型生成<br/>📁 service/llm_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict{role:str, content:str}], model:str, max_tokens:int=4096<br/>📤 出参: answer:str<br/>🔗 调用: LlmModel.get_model_client()"]

    G7 --> G8["📝 解析JSON结果<br/>📁 utils/text_utils.py<br/>🔧 TextUtil.get_json_from_text()<br/>📥 入参: text:str<br/>📤 出参: prettify_json:dict{result:str}<br/>🔗 调用: json.loads(), TextUtil.remove_think()"]

    G8 --> G9["🔄 更新chain_structure<br/>📁 service/label_merge_service.py<br/>🔧 chain_structure[generate_key] = {}<br/>📥 入参: generate_key:str, product_info:dict, source_list:list<br/>📤 出参: 更新后的chain_structure:dict<br/>🔗 调用: merge_and_deduplicate()"]

    G9 --> G10{"🔁 是否还有product<br/>📁 service/label_merge_service.py<br/>🔧 循环判断<br/>📥 入参: 循环状态:bool<br/>📤 出参: continue:bool<br/>🔗 调用: 无"}

    G10 -->|是| G2
    G10 -->|否| I["🌳 树结构转换<br/>📁 utils/tree_utils.py<br/>🔧 TreeUtils.restore_tree()<br/>📥 入参: nodes:dict<br/>📤 出参: tree:list<br/>🔗 调用: 无"]

    %% 合并流程
    H --> J["🔄 执行LLM合并<br/>📁 api/routes/label_merge.py<br/>🔧 perform_common_merge_helper()<br/>📥 入参: label_extract_info:dict, merge_func:callable, merge_process:str='LLM'<br/>📤 出参: label_merge_info:dict{chain_structure, node_companies, product}<br/>🔗 调用: merge_task_multirunner_helper(), asyncio.gather()"]
    I --> J

    J --> J1["📊 解析chain_structure<br/>📁 api/routes/label_merge.py<br/>🔧 提取节点、公司、产品标签<br/>📥 入参: label_extract_info:dict<br/>📤 出参: chain_structure:dict, company_abb_labels:list, product_label:list<br/>🔗 调用: 无"]

    J1 --> J2["⚡ 创建异步任务<br/>📁 api/routes/label_merge.py<br/>🔧 tasks = [merge_task_multirunner_helper(...)]<br/>📥 入参: merge_type:str, merge_array:list, merge_func:callable<br/>📤 出参: tasks:list[Task]<br/>🔗 调用: merge_task_multirunner_helper()"]

    J2 --> J3["🏃 多任务运行器<br/>📁 api/routes/label_merge.py<br/>🔧 merge_task_multirunner_helper()<br/>📥 入参: merge_type:str, merge_array:list, merge_func:callable<br/>📤 出参: (merge_type:str, result:dict)<br/>🔗 调用: llm_merge() | rule_merge()"]

    %% LLM合并分支
    J3 --> L1["🧠 LLM标签合并<br/>📁 service/label_merge_service.py<br/>🔧 LabelMergeService.llm_merge()<br/>📥 入参: label_array:list|dict, is_industry:bool=False<br/>📤 出参: merge_result:dict{result:list, 合并映射:dict}<br/>🔗 调用: answer_question(), get_json_from_text(), chains_merge_task()"]

    L1 --> L2["💭 构建系统提示词<br/>📁 configs/prompt_config.py<br/>🔧 PromptConfig.LABEL_COMPOSE_SYSTEM_PROMPT.format()<br/>📥 入参: prompt:str, label_array:str<br/>📤 出参: compose_system_prompt:str<br/>🔗 调用: 无"]

    L2 --> L3["🤖 大模型问答<br/>📁 service/llm_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict{role:str, content:str}], model:str, max_tokens:int=4096<br/>📤 出参: answer:str<br/>🔗 调用: LlmModel.get_model_client()"]

    L3 --> L4["📝 解析合并结果<br/>📁 utils/text_utils.py<br/>🔧 TextUtil.get_json_from_text()<br/>📥 入参: text:str<br/>📤 出参: prettify_json:dict{result:list}<br/>🔗 调用: json.loads(), TextUtil.remove_think()"]

    L4 --> M1["🔗 合并任务结果<br/>📁 api/routes/label_merge.py<br/>🔧 asyncio.gather(*tasks)<br/>📥 入参: tasks:list[Task]<br/>📤 出参: tasks_results:list[tuple(str, dict)]<br/>🔗 调用: asyncio.gather()"]

    %% 规则合并判断
    M1 --> K{"📋 检查merge_type参数<br/>📁 api/routes/label_merge.py<br/>🔧 request.merge_type判断<br/>📥 入参: merge_type:str<br/>📤 出参: 执行路径:str<br/>🔗 调用: 无"}

    K -->|Frequency| M["📊 执行频率规则合并<br/>📁 service/label_merge_service.py<br/>🔧 LabelMergeService.rule_merge()<br/>📥 入参: label_array:list, rule_type:str='num'<br/>📤 出参: rule_merge_result:dict{result:list, 合并映射:dict}<br/>🔗 调用: label_replace(), merge(), process_labels()"]

    K -->|Source| N["📁 执行来源规则合并<br/>📁 service/label_merge_service.py<br/>🔧 LabelMergeService.rule_merge()<br/>📥 入参: label_array:list, rule_type:str='value'<br/>📤 出参: rule_merge_result:dict{result:list, 合并映射:dict}<br/>🔗 调用: label_replace(), merge(), process_labels()"]

    %% 规则合并子流程
    M --> R1["🔄 标签替换接口<br/>📁 api/routes/company_full_name_supplement.py<br/>🔧 label_replace()<br/>📥 入参: input_labels:list[str], style:str='num'|'value'<br/>📤 出参: response:dict{result:list, 合并映射:dict}<br/>🔗 调用: 规则引擎相关方法"]
    N --> R1

    R1 --> R2["⚙️ 规则引擎处理<br/>📁 api/routes/company_full_name_supplement.py<br/>🔧 基于频率或来源的规则合并<br/>📥 入参: label_array:list, rule_type:str='num'|'value'<br/>📤 出参: merged_labels:list<br/>🔗 调用: 内部规则处理方法"]

    R2 --> O["🔄 规则合并处理<br/>📁 api/routes/label_merge.py<br/>🔧 perform_common_merge_helper()<br/>📥 入参: 规则合并结果:dict<br/>📤 出参: 处理后的结果:dict<br/>🔗 调用: 无"]
    K -->|无需规则合并| O

    %% 结果保存
    O --> P["🆔 生成UUID<br/>📁 utils/uuid_util.py<br/>🔧 UuidUtil.get_uuid()<br/>📥 入参: None<br/>📤 出参: unique_id:str<br/>🔗 调用: 无"]

    P --> P1["💾 保存结果到MongoDB<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.insert_one()<br/>🗄️ 表: LABEL_MERGE<br/>📥 入参: collection_name:str, doc_content:dict{_id, chain_structure, node_companies, product, request_param}<br/>📤 出参: insert_result:InsertOneResult<br/>🔗 调用: MongodbUtil.coll().insert_one()"]

    P1 --> Q["✅ 返回成功响应<br/>📁 entity/response_entity.py<br/>🔧 SuccessResponse()<br/>📥 入参: data:dict<br/>📤 出参: SuccessResponse{code:200, message:'success', data:dict}<br/>🔗 调用: 无"]

    %% 异常处理
    A --> E1["⚠️ 异常捕获<br/>📁 api/routes/label_merge.py<br/>🔧 try-except块<br/>📥 入参: Exception:object<br/>📤 出参: error_detail:str<br/>🔗 调用: LogUtil.error()"]
    E1 --> E2["📝 错误日志记录<br/>📁 utils/log_util.py<br/>🔧 LogUtil.error()<br/>📥 入参: msg:str<br/>📤 出参: None<br/>🔗 调用: 无"]
    E2 --> E3["❌ 返回错误响应<br/>📁 entity/response_entity.py<br/>🔧 FalseResponse()<br/>📥 入参: data:dict{error:str}<br/>📤 出参: FalseResponse{code:500, message:'error', data:dict}<br/>🔗 调用: 无"]

    %% 结束
    D --> Z[🏁 结束]
    Q --> Z
    E3 --> Z

    %% 样式定义
    style A fill:#e1f5fe
    style Z fill:#e8f5e8
    style G fill:#fff3e0
    style V1 fill:#fff9c4
    style V3 fill:#e8f5e8
    style G7 fill:#f3e5f5
    style L3 fill:#f3e5f5
    style R1 fill:#fff3e0
    style E1 fill:#ffebee
    style E3 fill:#ffcdd2
```

## 流程图说明

上述完整流程图展示了 `label_merge_perform` 接口的完整执行过程，包含以下主要模块：

### 🔄 主流程模块
- **缓存检查**: 优先检查是否有缓存结果，提高响应速度
- **历史数据获取**: 从MongoDB获取标签提取的历史数据
- **参数判断**: 根据请求参数决定执行路径

### 🤖 AI扩展模块
- **产品遍历**: 对每个产品-公司对执行AI扩展
- **向量检索**: 使用Milvus进行语义相似度检索
- **大模型生成**: 基于检索到的知识生成产业链位置
- **结构更新**: 更新产业链结构数据

### 🔀 标签合并模块
- **LLM合并**: 使用大模型进行智能标签合并
- **规则合并**: 基于频率或来源的规则合并
- **异步处理**: 并行处理多种类型的标签

### 💾 数据持久化模块
- **结果存储**: 将处理结果保存到MongoDB
- **UUID生成**: 为每个结果生成唯一标识
- **日志记录**: 详细记录处理过程

### ⚠️ 异常处理模块
- **异常捕获**: 全局异常处理机制
- **错误日志**: 详细的错误信息记录
- **错误响应**: 统一的错误响应格式

## 核心接口详细说明

### 主要文件和接口调用关系

#### 1. **api/routes/label_merge.py**

**label_merge_perform 主接口**
- **调用的其他接口**:
  - `LogUtil.log_json()` - 记录请求日志
  - `MongodbUtil.coll().find_one()` - 查询缓存
  - `MongodbUtil.query_doc_by_id()` - 获取历史数据
  - `LabelMergeService.ai_extend_perform()` - AI扩展
  - `perform_common_merge_helper()` - 标签合并
  - `MongodbUtil.insert_one()` - 保存结果
- **入参**: `LabelMergeRequest{mongodb_id, model, prompt, merge_type, is_ai_extend, is_cached}`
- **出参**: `SuccessResponse{code, message, data}` 或 `FalseResponse{code, message, data}`
- **查询表**: `LABEL_MERGE_PERFORM`, `LABEL_EXTRACT_PERFORM_HISTORY`, `LABEL_MERGE`

**perform_common_merge_helper 通用合并助手**
- **调用的其他接口**:
  - `merge_task_multirunner_helper()` - 多任务运行器
  - `asyncio.gather()` - 异步任务聚合
- **入参**: `{label_extract_info, merge_func, merge_process}`
- **出参**: `{chain_structure, node_companies, product, mongodb_id_node, mongodb_id_info}`

#### 2. **service/label_merge_service.py**

**LabelMergeService.ai_extend_perform AI扩展功能**
- **调用的其他接口**:
  - `ai_extend_pair()` - AI扩展配对
  - `merge_and_deduplicate()` - 数据合并去重
- **入参**: `{product: [{product_abb, company_abb}], key_companies: [{abb, name, is_listed, is_special, is_high_tech}]}`
- **出参**: `{chain_structure, mongodb_id_node, mongodb_id_info}`

**LabelMergeService.ai_extend_pair AI扩展配对**
- **调用的其他接口**:
  - `KbService.search_knowledge_by_question()` - 知识库检索
  - `Llm_Service.answer_question()` - 大模型问答
  - `TextUtil.get_json_from_text()` - JSON解析
- **入参**: `{product: str, company: str}`
- **出参**: `{generate_key: str, source_list: [{mongodb_id, source_name, source_file_title, source_file_url, source_content}]}`
- **查询表**: `KNOWLEDGE_REPORT_ALL` (Milvus向量库)

**LabelMergeService.llm_merge 大模型合并**
- **调用的其他接口**:
  - `Llm_Service.answer_question()` - 大模型问答
  - `TextUtil.get_json_from_text()` - JSON解析
  - `chains_merge_task()` - 产业链合并任务
- **入参**: `{label_array: list, is_industry: bool}`
- **出参**: `{result: list, 合并后标签: [原始标签列表]}`

**LabelMergeService.rule_merge 规则合并**
- **调用的其他接口**:
  - `label_replace()` - 标签替换接口
  - `merge()` - 规则合并方法
  - `process_labels()` - 标签处理方法
- **入参**: `{label_array: list, is_industry: bool}`
- **出参**: `{result: list, 合并后标签: [原始标签列表]}`

#### 3. **service/llm_service.py**

**Llm_Service.answer_question 大模型问答接口**
- **调用的其他接口**:
  - `LlmModel.get_model_client()` - 获取模型客户端
- **入参**: `{messages: List[dict], model: str, max_tokens: int}`
- **出参**: `str` (大模型回复内容)

#### 4. **service/kb_service.py**

**KbService.search_knowledge_by_question 知识库检索接口**
- **调用的其他接口**:
  - `TextEmbedService.text_embedding()` - 文本向量化
  - `MilvusUtil.search_by_vector()` - 向量检索
- **入参**: `{collection_name: str, question: str, limit_top_k: int, expr: str}`
- **出参**: `[{mongodb_id, file_source, file_title, file_url, file_time, chunk_content, chunk_content_father, similarity_score}]`
- **查询表**: `KNOWLEDGE_REPORT_ALL` (Milvus向量库)

#### 5. **utils/mongodb_util.py**

**MongodbUtil.insert_one 插入单条数据**
- **入参**: `{collection_name: str, doc_content: dict}`
- **出参**: `insert_result`

**MongodbUtil.find_one 查询单条数据**
- **入参**: `{collection_name: str, query_filter: dict}`
- **出参**: `document` 或 `None`

**MongodbUtil.coll 获取集合对象**
- **入参**: `{collection_name: str}`
- **出参**: `Collection对象`

#### 6. **utils/milvus_util.py**

**MilvusUtil.search_by_vector 向量检索**
- **调用的其他接口**:
  - `self.milvus_client.search()` - Milvus客户端检索
- **入参**: `{collection_name: str, vector: list, limit_top_k: int}`
- **出参**: `search_results`

**MilvusUtil.collection_is_exists 检查集合存在性**
- **入参**: `{collection_name: str}`
- **出参**: `bool`

### 关键功能模块详细说明

#### 1. 缓存机制
- **缓存键**: 基于请求参数组合 `{mongodb_id, model, prompt, merge_type, is_ai_extend, is_cached}`
- **缓存存储**: MongoDB `LABEL_MERGE_PERFORM` 集合
- **缓存查询**: `MongodbUtil.coll().find_one()` 方法
- **缓存数据结构**:
```python
{
    "raw_label_merge_data_id": str,  # 原始数据ID
    "model": str,                    # 模型名称
    "prompt": str,                   # 提示词
    "merge_type": str,               # 合并类型
    "is_ai_extend": bool,            # AI扩展标志
    "is_cached": bool,               # 缓存标志
    "result_data": dict,             # 缓存的结果数据
    "create_time": datetime          # 创建时间
}
```

#### 2. AI扩展功能
- **触发条件**: `is_ai_extend=True` 且 `chain_structure` 为空
- **处理流程**: 产品遍历 → 向量检索 → 大模型生成 → 结构更新
- **知识库检索**: 使用Milvus向量数据库进行语义相似度匹配
- **大模型生成**: 基于检索到的知识生成产业链位置
- **输入数据结构**:
```python
{
    "product": [
        {
            "product_abb": str,      # 产品简称
            "company_abb": str       # 公司简称
        }
    ]
}
```
- **输出数据结构**:
```python
{
    "chain_structure": {
        "产业链名称": {
            "product": [{"product_abb": str, "company_abb": str}],
            "company": [],
            "source_list": [{"_id": str, "type": str, "title": str, "file_url": str}]
        }
    }
}
```

#### 3. 标签合并策略
- **LLM合并**:
  - 使用大模型进行智能标签合并
  - 支持产业链、公司、产品三种类型标签
  - 并行处理提高效率
  - 输入: `{label_array: list, is_industry: bool}`
  - 输出: `{result: list, 合并映射: dict}`

- **规则合并**:
  - 基于频率(Frequency)或来源(Source)的规则合并
  - 调用 `label_replace` 接口执行规则引擎
  - 支持层级标签处理
  - 输入: `{label_array: list, rule_type: str}`
  - 输出: `{result: list, 合并映射: dict}`

#### 4. 数据持久化
- **MongoDB存储**:
  - `LABEL_MERGE_PERFORM`: 缓存数据
  - `LABEL_EXTRACT_PERFORM_HISTORY`: 历史数据查询
  - `LABEL_MERGE`: 最终结果存储
- **Milvus存储**:
  - `KNOWLEDGE_REPORT_ALL`: 向量化的知识库内容
  - 支持语义相似度检索
- **数据一致性**: UUID唯一标识，完整的数据关联



## 详细流程说明

### 1. 请求参数验证
接口接收 `LabelMergeRequest` 对象，包含以下关键参数：
- `mongodb_id`: 标签提取返回的ID，用于获取历史数据
- `model`: 大模型名称（如 "qwen2.5-72B", "DeepSeek-R1-Distill-Qwen-32B"）
- `prompt`: 自定义提示词
- `merge_type`: 合并类型（"Frequency" 或 "Source"）
- `is_ai_extend`: 是否启用AI扩展功能
- `is_cached`: 是否使用缓存

### 2. 缓存查询机制
系统首先在 `LABEL_MERGE_PERFORM` 集合中查询是否存在相同参数的缓存：
```python
cached_data = MongodbUtil.coll(CollectionConfig.LABEL_MERGE_PERFORM).find_one({
    "raw_label_merge_data_id": request.mongodb_id,
    "model": request.model,
    "prompt": request.prompt,
    "merge_type": request.merge_type,
    "is_ai_extend": request.is_ai_extend,
    "is_cached": request.is_cached
}, sort=[('$natural', -1)])
```

### 3. AI扩展详细流程
当 `is_ai_extend=True` 且 `chain_structure` 为空时：

1. **遍历产品列表**: 对每个产品-公司对执行扩展
2. **向量检索**: 使用 `KbService` 在知识库中检索相关文档
3. **大模型生成**: 基于检索到的知识生成产业链位置
4. **结构更新**: 将生成的结果更新到 `chain_structure`
5. **树结构转换**: 使用 `TreeUtils.restore_tree` 转换为JSON格式

### 4. 标签合并策略详解

#### LLM合并流程：
1. **数据准备**: 提取节点、公司、产品标签
2. **并行处理**: 使用 `asyncio.gather` 并行处理三类标签
3. **大模型调用**: 每类标签独立调用大模型进行合并
4. **结果整合**: 将合并结果重新组织为完整结构

#### 规则合并流程：
1. **规则选择**: 根据 `merge_type` 选择合并规则
   - "Frequency": 基于频率的合并
   - "Source": 基于来源的合并
2. **接口调用**: 调用 `label_replace` 接口执行规则合并
3. **结果处理**: 处理合并结果并进行一致性检查

### 5. 数据持久化策略
- **结果存储**: 将最终合并结果存储到 `LABEL_MERGE` 集合
- **UUID生成**: 使用 `UuidUtil.get_uuid()` 生成唯一标识
- **日志记录**: 详细记录处理过程和结果

## 数据表操作详细信息

### MongoDB 集合操作

| 集合名称 | 操作类型 | 接口方法 | 查询条件 | 数据结构 | 用途说明 |
|---------|---------|----------|----------|----------|----------|
| LABEL_MERGE_PERFORM | 查询 | find_one() | `{raw_label_merge_data_id, model, prompt, merge_type, is_ai_extend, is_cached}` | 缓存结果数据 | 缓存查询，避免重复计算 |
| LABEL_EXTRACT_PERFORM_HISTORY | 查询 | query_doc_by_id() | `{_id: mongodb_id}` | 标签提取历史数据 | 获取原始标签数据 |
| LABEL_MERGE | 插入 | insert_one() | 无 | 标签融合结果 | 保存最终处理结果 |

### Milvus 向量库操作

| 集合名称 | 操作类型 | 接口方法 | 查询条件 | 数据结构 | 用途说明 |
|---------|---------|----------|----------|----------|----------|
| KNOWLEDGE_REPORT_ALL | 向量检索 | search_by_vector() | `{vector, limit_top_k=3}` | 知识库文档向量 | 语义相似度检索 |

### 详细数据结构说明

#### LABEL_MERGE_PERFORM (缓存表)
```python
{
    "_id": str,                          # MongoDB文档ID
    "raw_label_merge_data_id": str,      # 原始标签合并数据ID
    "model": str,                        # 大模型名称
    "prompt": str,                       # 自定义提示词
    "merge_type": str,                   # 合并类型
    "is_ai_extend": bool,                # AI扩展标志
    "is_cached": bool,                   # 缓存标志
    "result_data": {                     # 缓存的结果数据
        "chain_structure": dict,         # 产业链结构
        "node_companies": dict,          # 节点公司映射
        "product": list,                 # 产品信息
        "mongodb_id_node": dict,         # 节点ID映射
        "mongodb_id_info": dict          # 信息ID映射
    },
    "create_time": datetime,             # 创建时间
    "update_time": datetime              # 更新时间
}
```

#### LABEL_EXTRACT_PERFORM_HISTORY (历史数据表)
```python
{
    "_id": str,                          # MongoDB文档ID
    "chain_structure": dict,             # 产业链结构数据
    "node_companies": {                  # 节点公司映射
        "节点名称": [
            {
                "abb": str,              # 公司简称
                "name": str,             # 公司全名
                "is_listed": bool,       # 是否上市
                "is_special": bool,      # 是否特殊公司
                "is_high_tech": bool     # 是否高新技术企业
            }
        ]
    },
    "product": [                         # 产品信息列表
        {
            "product_abb": str,          # 产品简称
            "company_abb": str           # 公司简称
        }
    ],
    "key_companies": list,               # 关键公司列表
    "mongodb_id_node": dict,             # MongoDB节点ID映射
    "mongodb_id_info": dict              # MongoDB信息ID映射
}
```

#### KNOWLEDGE_REPORT_ALL (知识库向量表)
```python
{
    "id": str,                           # 向量ID
    "mongodb_id": str,                   # 对应MongoDB文档ID
    "file_source": str,                  # 文件来源
    "file_title": str,                   # 文件标题
    "file_url": str,                     # 文件URL
    "file_time": str,                    # 文件时间
    "chunk_content": str,                # 文档片段内容
    "chunk_content_father": str,         # 父级文档内容
    "vector": list,                      # 文档向量
    "metadata": dict                     # 元数据信息
}
```

## 技术特点

1. **异步处理**: 大量使用async/await提高并发性能
2. **多任务并行**: 使用asyncio.gather并行处理多个合并任务
3. **智能缓存**: 基于参数组合的智能缓存机制
4. **向量检索**: 结合Milvus进行语义相似度检索
5. **大模型集成**: 深度集成LLM进行智能标签处理
6. **规则引擎**: 支持多种规则合并策略
7. **容错机制**: 完善的异常处理和日志记录
8. **数据一致性**: 多层次的数据验证和一致性检查



## 数据流转图

```mermaid
flowchart LR
    A[请求参数] --> B[缓存检查]
    B -->|命中| C[返回缓存数据]
    B -->|未命中| D[历史数据查询]
    D --> E[标签提取数据]
    E --> F{AI扩展判断}
    F -->|需要扩展| G[向量检索]
    G --> H[知识库文档]
    H --> I[大模型生成]
    I --> J[产业链结构]
    F -->|不需要扩展| K[LLM合并]
    J --> K
    K --> L[合并后标签]
    L --> M{规则合并判断}
    M -->|需要规则合并| N[规则引擎]
    N --> O[最终合并结果]
    M -->|不需要规则合并| O
    O --> P[MongoDB存储]
    P --> Q[响应结果]

    style A fill:#e3f2fd
    style G fill:#fff9c4
    style I fill:#f3e5f5
    style N fill:#fff3e0
    style P fill:#e8f5e8
    style Q fill:#e8f5e8
```

## 关键数据结构

### 输入数据结构
```json
{
  "mongodb_id": "标签提取ID",
  "model": "大模型名称",
  "prompt": "自定义提示词",
  "merge_type": "Frequency|Source",
  "is_ai_extend": true|false,
  "is_cached": true|false
}
```

### 标签提取历史数据结构
```json
{
  "chain_structure": {},
  "node_companies": {},
  "product": [
    {
      "product_abb": "产品简称",
      "company_abb": "公司简称"
    }
  ],
  "key_companies": [],
  "mongodb_id_node": {},
  "mongodb_id_info": {}
}
```

### AI扩展生成结构
```json
{
  "产业链名称": {
    "product": [
      {
        "product_abb": "产品简称",
        "company_abb": "公司简称"
      }
    ],
    "company": [],
    "source_list": [
      {
        "_id": "文档ID",
        "type": "研报",
        "title": "文档标题",
        "file_url": "文件URL"
      }
    ]
  }
}
```

### 最终输出结构
```json
{
  "_id": "融合结果ID",
  "chain_structure": "产业链结构",
  "chain_structure_json": "JSON格式产业链",
  "chain_structure_llm_merge": "LLM合并结果",
  "node_companies": "节点公司信息",
  "product": "产品信息",
  "mongodb_id_node": "节点映射",
  "mongodb_id_info": "信息映射",
  "request_param": "请求参数"
}
```

## 性能优化点

1. **缓存机制**: 避免重复计算，提高响应速度
2. **并行处理**: 多任务并行执行，提高处理效率
3. **向量检索**: 快速语义匹配，提高检索精度
4. **批量操作**: 减少数据库交互次数
5. **异步IO**: 非阻塞IO操作，提高系统吞吐量

## 总结

`label_merge_perform` 接口是一个复杂的标签融合系统，集成了多种先进技术：

1. **智能化**: 通过大模型实现智能标签合并
2. **知识驱动**: 基于向量检索的知识库增强
3. **多策略**: 支持LLM和规则两种合并策略
4. **高性能**: 异步并行处理，智能缓存机制
5. **可扩展**: 模块化设计，易于扩展新功能

该系统在产业链分析、标签管理等场景中具有重要应用价值。
