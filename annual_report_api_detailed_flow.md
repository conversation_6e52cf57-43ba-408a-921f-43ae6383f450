# annual_report_api 详细流程分析

## 概述

`annual_report_api` 是一个年报信息提取接口，集成了向量检索、大模型分析、数据结构化等多种技术。本文档详细展示了接口调用关系、入参出参、数据表操作等信息。

## 1. 主流程图

```mermaid
flowchart TD
    A["🚀 开始: annual_report_info_ext<br/>📁 api/routes/annual_report_api.py<br/>🔧 annual_report_info_ext()<br/>📥 入参: AnnualReportEntity{company_name:str}<br/>📤 出参: SuccessResponse{code:int, message:str, data:dict} | FalseResponse{code:int, message:str, data:dict}<br/>🔗 调用: AnnualReportService.common_build_ext()"] --> B["� PDF文件名映射<br/>📁 api/routes/annual_report_api.py<br/>🔧 pdf_name_to_filename.get()<br/>📥 入参: company_name:str<br/>📤 出参: pdf_filename:str<br/>🔗 调用: 无"]
    
    B --> C["🔄 通用构建提取<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportService.common_build_ext()<br/>📥 入参: company_name:str, pdf_filename:str, year:str='2023', industry_chain:str='工业机器人', collection:str<br/>📤 出参: annual_report_data:dict<br/>🔗 调用: MongodbUtil.query_docs_by_condition(), AnnualReportCompanyAnalysis()"]

    C --> D{"� 检查历史缓存<br/>📁 service/annual_report_service.py<br/>🔧 MongodbUtil.query_docs_by_condition()<br/>�️ 表: ANNUAL_REPORT_HISTORY<br/>�📥 入参: collection_name:str, search_condition:dict{company_name, year}<br/>📤 出参: result:list<br/>🔗 调用: MongodbUtil.connect()"}

    D -->|有缓存数据| E["✅ 返回缓存结果<br/>📁 service/annual_report_service.py<br/>🔧 return result[0]<br/>📥 入参: cached_data:dict<br/>📤 出参: cached_result:dict<br/>🔗 调用: 无"]

    D -->|无缓存数据| F["� 初始化分析工具<br/>📁 service/annual_report_service.py<br/>🔧 实例化分析工具<br/>📥 入参: collection_name:str<br/>📤 出参: milvus_util, embutil, com_analysis<br/>🔗 调用: MilvusUtil(), TextEmbedService(), AnnualReportCompanyAnalysis()"]

    F --> G["👨‍👩‍👧‍👦 母公司分析<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_parentand()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: parentand_result:dict<br/>🔗 调用: common_rag(), answer_question()"]

    G --> H["� 子公司分析<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_subsidiary()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: subsidiary_result:dict<br/>🔗 调用: common_rag(), answer_question()"]

    H --> I["🤝 合营企业分析<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_joint_venture()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: joint_venture_result:dict<br/>🔗 调用: common_rag(), answer_question()"]

    I --> J["🤝 联营企业分析<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_associate()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: associate_result:dict<br/>🔗 调用: common_rag(), answer_question()"]

    J --> K["� 采购商品分析<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_purchase_goods()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: purchase_result:dict<br/>🔗 调用: common_rag(), answer_question()"]

    K --> L["� 出售商品分析<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_sell_goods()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: sell_result:dict<br/>🔗 调用: common_rag(), answer_question()"]

    L --> M["🏠 租赁分析<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_lease()<br/>� 入参: company_name:str, pdf_filename:str<br/>📤 出参: lease_result:dict<br/>🔗 调用: common_rag(), answer_question()"]

    M --> N["🛡️ 担保分析<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_guarantee()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: guarantee_result:dict<br/>🔗 调用: common_rag(), answer_question()"]

    N --> O["📊 数据结构化<br/>📁 service/annual_report_service.py<br/>🔧 构建results_list和final_list<br/>📥 入参: 所有分析结果:dict<br/>📤 出参: structured_data:dict{results_list, final_list}<br/>🔗 调用: 数据整合方法"]

    O --> P["💾 保存结果<br/>📁 service/annual_report_service.py<br/>🔧 MongodbUtil.insert_one()<br/>🗄️ 表: ANNUAL_REPORT_HISTORY<br/>📥 入参: collection_name:str, doc_content:dict<br/>📤 出参: insert_result<br/>🔗 调用: MongodbUtil.coll().insert_one()"]

    P --> Q["✅ 返回成功响应<br/>📁 service/annual_report_service.py<br/>🔧 return save_data<br/>📥 入参: complete_data:dict<br/>📤 出参: save_data:dict<br/>🔗 调用: 无"]

    %% 异常处理
    A --> R["⚠️ 异常捕获<br/>📁 api/routes/annual_report_api.py<br/>🔧 try-except块<br/>📥 入参: Exception<br/>📤 出参: error_detail:str<br/>🔗 调用: 无"]
    R --> S["❌ 错误响应<br/>📁 entity/annual_report_entity.py<br/>🔧 FalseResponse()<br/>📥 入参: data:dict{error:str}<br/>📤 出参: FalseResponse{code:500, message:'error', data:dict}<br/>🔗 调用: 无"]

    E --> Z[🏁 结束]
    Q --> Z
    S --> Z
    
    style A fill:#e1f5fe
    style Z fill:#e8f5e8
    style C fill:#fff3e0
    style G fill:#fff9c4
    style H fill:#f3e5f5
    style I fill:#e8f5e8
    style R fill:#ffebee
    style S fill:#ffcdd2
```

## 2. 关联公司分析子流程图

```mermaid
flowchart TD
    A["🏢 关联公司分析开始<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: analysis_results:dict<br/>🔗 调用: common_rag(), answer_question()"] --> B["❓ 构建查询问题<br/>📁 service/annual_report_service.py<br/>🔧 构建query字符串<br/>📥 入参: company_name:str, analysis_type:str<br/>📤 出参: query:str<br/>🔗 调用: 无"]

    B --> C["🧮 文本向量化<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: texts:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]

    C --> D["� RAG检索<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.common_rag()<br/>�️ 表: 指定Milvus集合<br/>�📥 入参: query:str, pdf_filename:str, keywords:list[str]<br/>📤 出参: documents:str, is_hit:bool<br/>🔗 调用: search_by_vector(), reranker.rerank()"]

    D --> E["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ 向量库: 指定Milvus集合<br/>📥 入参: collection_name:str, vector:list[float], limit:int, filter:str<br/>📤 出参: search_results:list[dict]<br/>🔗 调用: milvus_client.search()"]

    E --> F["� 重排序<br/>📁 utils/reranker_util.py<br/>🔧 QueryReranker.rerank()<br/>📥 入参: query:str, documents:list, top_k:int<br/>📤 出参: reranked_docs:list<br/>🔗 调用: reranker模型"]

    F --> G["🤖 大模型分析<br/>📁 service/annual_report_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict{role:str, content:str}], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]

    G --> H["� 解析结果<br/>📁 service/annual_report_service.py<br/>🔧 解析LLM输出<br/>📥 入参: response:str<br/>📤 出参: parsed_result:list[str]<br/>🔗 调用: 字符串处理方法"]

    H --> I["✅ 返回分析结果<br/>📁 service/annual_report_service.py<br/>🔧 return analysis_result<br/>📥 入参: 完整数据:dict<br/>📤 出参: analysis_result:list[str]<br/>🔗 调用: 无"]

    style A fill:#e1f5fe
    style D fill:#fff9c4
    style E fill:#e8f5e8
    style G fill:#f3e5f5
    style I fill:#e8f5e8
```

## 3. 数据结构化和保存流程图

```mermaid
flowchart TD
    A["�️ 数据结构化开始<br/>📁 service/annual_report_service.py<br/>🔧 构建results_list和final_list<br/>� 入参: 所有分析结果:dict<br/>� 出参: structured_data:dict<br/>🔗 调用: 数据整合方法"] --> B["� 整合关联公司信息<br/>📁 service/annual_report_service.py<br/>🔧 构建results_list<br/> 入参: 各类分析结果:dict<br/>📤 出参: results_list:list[str]<br/>🔗 调用: 字符串拼接方法"]

    B --> C["� 构建详细信息<br/>📁 service/annual_report_service.py<br/>🔧 构建final_list<br/>📥 入参: 各类分析结果:dict<br/>📤 出参: final_list:list[dict]<br/>🔗 调用: 数据格式化方法"]

    C --> D["🆔 生成UUID<br/>📁 service/annual_report_service.py<br/>🔧 uuid.uuid1().hex<br/>📥 入参: 无<br/>📤 出参: unique_id:str<br/>🔗 调用: uuid.uuid1()"]

    D --> E["� 构建保存数据<br/>📁 service/annual_report_service.py<br/>🔧 构建save_data<br/>📥 入参: 所有处理结果:dict<br/>📤 出参: save_data:dict{_id, company_name, year, 公告url, 公司数量, 年报关联公司汇总, 待入库具体信息}<br/> 调用: 无"]

    E --> F["� 检查历史数据<br/>📁 service/annual_report_service.py<br/>🔧 MongodbUtil.query_docs_by_condition()<br/>�️ 表: ANNUAL_REPORT_HISTORY<br/>� 入参: collection_name:str, search_condition:dict{company_name, year}<br/>📤 出参: result:list<br/>🔗 调用: MongodbUtil.connect()"]

    F -->|无历史数据| G["� 保存到MongoDB<br/>📁 service/annual_report_service.py<br/>🔧 MongodbUtil.insert_one()<br/>🗄️ 表: ANNUAL_REPORT_HISTORY<br/>📥 入参: collection_name:str, doc_content:dict<br/>📤 出参: insert_result<br/>🔗 调用: MongodbUtil.coll().insert_one()"]

    F -->|有历史数据| H["� 跳过保存<br/>📁 service/annual_report_service.py<br/>🔧 print('has data')<br/>📥 入参: 无<br/>📤 出参: 无<br/>🔗 调用: 无"]

    G --> I["✅ 返回结构化结果<br/>📁 service/annual_report_service.py<br/>🔧 return save_data<br/>📥 入参: 完整数据:dict<br/>📤 出参: save_data:dict<br/>🔗 调用: 无"]

    H --> I

    style A fill:#e1f5fe
    style F fill:#e3f2fd
    style G fill:#fff3e0
    style I fill:#e8f5e8
```

## 5. 详细接口调用信息

### 5.1 annual_report_info_ext 主接口
**文件位置**: `api/routes/annual_report_api.py`
**方法名**: `annual_report_info_ext()`

**调用的其他接口**:
1. `pdf_name_to_filename.get()` - PDF文件名映射
2. `AnnualReportService.common_build_ext()` - 通用构建提取

**详细入参**:
```python
{
    "company_name": str         # 公司名称，从"中国石油","南钢股份","天奇股份","中国石化"中选择
}
```

**详细出参**:
```python
# 成功响应 (SuccessResponse)
{
    "code": int,                # 响应状态码，200表示成功
    "message": str,             # 响应消息
    "data": {                   # 年报关联公司分析结果
        "_id": str,             # 唯一标识
        "company_name": str,    # 公司名称
        "year": str,           # 年份，固定为"2023"
        "公告url": str,        # 年报文件URL
        "公司数量": int,        # 提取的关联公司总数
        "年报关联公司汇总": list, # 关联公司汇总信息
        "待入库具体信息": list,  # 详细的关联公司信息
        "file_time": str,      # 文件时间
        "source_id": str       # 来源ID
    }
}
```

### 5.2 annual_report_info_ext_with_file 接口
**文件位置**: `api/routes/annual_report_api.py`
**方法名**: `annual_report_info_ext_with_file()`

**调用的其他接口**:
1. `MongodbUtil.query_doc_by_id()` - 根据mongodb_id查询文档信息
2. `AnnualReportService.common_build_ext()` - 通用构建提取

**详细入参**:
```python
{
    "company_name": str,        # 公司名称
    "year": str,               # 年份
    "pdf_filename": str,       # PDF文件名
    "industry_chain": str,     # 产业链，默认"工业机器人"
    "mongodb_id": str,         # MongoDB文档ID，可选
    "milvus_collection": str,  # Milvus集合名称
    "is_cached": bool          # 是否使用缓存，默认True
}
```

**查询的数据表**:
1. `ANNUAL_REPORT_HISTORY` - 历史数据查询和结果保存
2. `notice_label_info` - 文档信息查询（当提供mongodb_id时）
3. 指定的Milvus集合 - 向量检索

### 5.3 common_build_ext 接口
**文件位置**: `service/annual_report_service.py`
**方法名**: `AnnualReportService.common_build_ext()`

**调用的其他接口**:
1. `MongodbUtil.query_docs_by_condition()` - 历史数据查询
2. `MilvusUtil()` - Milvus工具实例化
3. `TextEmbedService()` - 文本嵌入服务实例化
4. `AnnualReportCompanyAnalysis()` - 年报公司分析实例化
5. `company_parentand()` - 母公司分析
6. `company_subsidiary()` - 子公司分析
7. `company_joint_venture()` - 合营企业分析
8. `company_associate()` - 联营企业分析
9. `company_purchase_goods()` - 采购商品分析
10. `company_sell_goods()` - 出售商品分析
11. `company_lease()` - 租赁分析
12. `company_guarantee()` - 担保分析
13. `MongodbUtil.insert_one()` - 结果保存

**详细入参**:
```python
{
    "company_name": str,        # 公司名称
    "pdf_filename": str,       # PDF文件名
    "year": str,               # 年份
    "industry_chain": str,     # 产业链
    "collection": str,         # Milvus集合名称
    "mongodb_id": str,         # MongoDB文档ID，可选
    "is_cached": bool          # 是否使用缓存
}
```

**详细出参**:
```python
{
    "公告url": str,            # 年报文件URL
    "公司数量": int,           # 关联公司总数
    "年报关联公司汇总": list,   # 关联公司汇总
    "待入库具体信息": list,     # 详细关联公司信息
    "company_name": str,       # 公司名称
    "file_time": str,         # 文件时间
    "source_id": str,         # 来源ID
    "_id": str,               # 唯一标识
    "year": str               # 年份
}
```

### 5.4 common_rag 接口
**文件位置**: `service/annual_report_service.py`
**方法名**: `AnnualReportCompanyAnalysis.common_rag()`

**调用的其他接口**:
1. `TextEmbedService.text_embedding()` - 文本向量化
2. `MilvusUtil.search_by_vector()` - 向量检索
3. `QueryReranker.rerank()` - 重排序

**详细入参**:
```python
{
    "query": str,              # 检索问题
    "pdf_filename": str,       # PDF文件名
    "keywords": list[str]      # 关键词列表
}
```

**详细出参**:
```python
{
    "documents": str,          # 检索到的文档内容
    "is_hit": bool            # 是否命中关键词
}
```

**查询的数据表**:
1. 指定的Milvus集合 - 向量检索

## 6. 核心数据结构

### 6.1 请求参数结构 (AnnualReportEntity)
```json
{
  "company_name": "公司名称，从'中国石油','南钢股份','天奇股份','中国石化'中选择"
}
```

### 6.2 请求参数结构 (AnnualReportFileEntity)
```json
{
  "company_name": "公司名称",
  "year": "年份，如2023",
  "pdf_filename": "PDF文件名",
  "industry_chain": "产业链，默认'工业机器人'",
  "mongodb_id": "MongoDB文档ID，可选",
  "milvus_collection": "Milvus集合名称",
  "is_cached": "是否使用缓存，默认true"
}
```

### 6.3 关联公司分析结果结构
```json
{
  "母公司": ["母公司A", "母公司B"],
  "子公司": ["子公司A", "子公司B", "子公司C"],
  "合营企业": ["合营企业A"],
  "联营企业": ["联营企业A", "联营企业B"],
  "采购商品或接受劳务": ["供应商A", "供应商B"],
  "出售商品或提供劳务": ["客户A", "客户B"],
  "租赁": ["租赁方A"],
  "担保": ["担保方A", "担保方B"]
}
```

### 6.4 最终输出结构
```json
{
  "_id": "唯一标识",
  "company_name": "公司名称",
  "year": "年份",
  "公告url": "年报文件URL",
  "公司数量": 25,
  "年报关联公司汇总": [
    "母公司: 母公司A, 母公司B",
    "子公司: 子公司A, 子公司B, 子公司C",
    "合营企业: 合营企业A",
    "联营企业: 联营企业A, 联营企业B",
    "采购商品或接受劳务: 供应商A, 供应商B",
    "出售商品或提供劳务: 客户A, 客户B",
    "租赁: 租赁方A",
    "担保: 担保方A, 担保方B"
  ],
  "待入库具体信息": [
    {
      "industry_chain": "工业机器人",
      "company_name": "目标公司",
      "affiliate": "关联公司名称",
      "relation_type": "母公司|子公司|合营企业|联营企业|其他关联方|采购商品/接受劳务|出售商品/提供劳务|租赁|担保",
      "affiliate_register_address": "关联公司注册地址",
      "affiliate_business_address": "关联公司经营地址"
    }
  ],
  "file_time": "文件时间",
  "source_id": "来源ID"
}
```

## 7. 数据表操作汇总

| 操作类型 | 表名 | 操作方法 | 用途 | 入参 | 出参 |
|---------|------|----------|------|------|------|
| 查询 | ANNUAL_REPORT_HISTORY | query_docs_by_condition() | 历史数据查询 | search_condition | result_list |
| 插入 | ANNUAL_REPORT_HISTORY | insert_one() | 结果保存 | doc_content | insert_result |
| 查询 | notice_label_info | query_doc_by_id() | 文档信息查询 | doc_id | doc_info |
| 检索 | 指定Milvus集合 | search_by_vector() | 向量检索 | vector, limit, filter | search_results |

## 8. 技术特点

1. **预定义文件映射**: 支持从预定义的公司名称映射到对应的年报文件
2. **多维度关联分析**: 母公司、子公司、合营企业、联营企业、采购、出售、租赁、担保等8个维度的全面分析
3. **RAG增强检索**: 基于向量检索和关键词匹配的精准信息提取，提高准确性
4. **智能缓存**: 基于公司名称和年份的缓存机制，避免重复处理
5. **结构化输出**: 标准化的年报关联公司数据结构，便于后续处理
6. **异常处理**: 完善的错误处理和日志记录机制
7. **重排序优化**: 使用重排序模型提高检索结果质量
8. **灵活配置**: 支持自定义Milvus集合和缓存控制
9. **数据清洗**: 完善的数据清洗和验证流程
