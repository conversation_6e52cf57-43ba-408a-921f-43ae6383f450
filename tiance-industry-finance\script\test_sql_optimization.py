#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 15:00:00
# <AUTHOR> Assistant
# @File         : test_sql_optimization.py
# @Description  : 测试 SQL 优化后的统计查询性能和正确性
"""

import sys
import os
import time

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from service_data_manage.service.data_manage_service import CompanyDataStatsService
from utils.sql_util import SQLUtil
from entity.mysql_entity import CompanyMain
from sqlalchemy import and_, func
from datetime import datetime, date
from utils.log_util import LogUtil


def test_sql_optimization():
    """测试 SQL 优化后的统计查询"""
    try:
        print("=" * 60)
        print("测试 SQL 优化后的统计查询")
        print("=" * 60)
        
        # 初始化日志
        LogUtil.init(process_name="test_sql_optimization")
        
        # 连接数据库
        SQLUtil.connect()
        
        # 1. 测试优化后的统计查询
        print("\n1. 测试优化后的统计查询...")
        print("-" * 40)
        
        start_time = time.time()
        stats_result = CompanyDataStatsService.get_company_data_statistics()
        end_time = time.time()
        
        print(f"✓ 统计查询完成，耗时: {end_time - start_time:.3f} 秒")
        print(f"  - 公司总数: {stats_result.get('total_count', 'N/A')}")
        print(f"  - 上市公司数: {stats_result.get('listed_count', 'N/A')}")
        print(f"  - 今日更新数: {stats_result.get('today_updated_count', 'N/A')}")
        print(f"  - 查询日期: {stats_result.get('query_date', 'N/A')}")
        
        # 2. 验证 SQL 查询的正确性
        print("\n2. 验证 SQL 查询的正确性...")
        print("-" * 40)
        
        # 验证公司总数
        total_count_sql = SQLUtil.count_records_with_condition(
            CompanyMain, 
            CompanyMain.status == 1
        )
        print(f"✓ 公司总数 SQL 查询: {total_count_sql}")
        
        # 验证上市公司数
        listed_count_sql = SQLUtil.count_records_with_condition(
            CompanyMain,
            and_(
                CompanyMain.status == 1,
                CompanyMain.StockAbbr.isnot(None),
                CompanyMain.StockAbbr != '',
                CompanyMain.StockAbbr != 'NULL',
                func.trim(CompanyMain.StockAbbr) != ''
            )
        )
        print(f"✓ 上市公司数 SQL 查询: {listed_count_sql}")
        
        # 验证今日更新数
        today = date.today()
        today_start = datetime.combine(today, datetime.min.time())
        
        today_updated_count_sql = SQLUtil.count_records_with_condition(
            CompanyMain,
            and_(
                CompanyMain.status == 1,
                CompanyMain.update_time >= today_start
            )
        )
        print(f"✓ 今日更新数 SQL 查询: {today_updated_count_sql}")
        
        # 3. 对比结果一致性
        print("\n3. 对比结果一致性...")
        print("-" * 40)
        
        consistency_check = True
        
        if stats_result.get('total_count') == total_count_sql:
            print("✓ 公司总数一致")
        else:
            print(f"✗ 公司总数不一致: 服务层={stats_result.get('total_count')}, SQL={total_count_sql}")
            consistency_check = False
        
        if stats_result.get('listed_count') == listed_count_sql:
            print("✓ 上市公司数一致")
        else:
            print(f"✗ 上市公司数不一致: 服务层={stats_result.get('listed_count')}, SQL={listed_count_sql}")
            consistency_check = False
        
        if stats_result.get('today_updated_count') == today_updated_count_sql:
            print("✓ 今日更新数一致")
        else:
            print(f"✗ 今日更新数不一致: 服务层={stats_result.get('today_updated_count')}, SQL={today_updated_count_sql}")
            consistency_check = False
        
        if consistency_check:
            print("🎉 所有统计结果一致，SQL 优化成功！")
        else:
            print("❌ 统计结果不一致，需要检查 SQL 查询逻辑")
        
        return consistency_check
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            SQLUtil.close()
        except:
            pass


def test_sql_performance():
    """测试 SQL 查询性能"""
    print("\n" + "=" * 60)
    print("测试 SQL 查询性能")
    print("=" * 60)
    
    try:
        SQLUtil.connect()
        
        # 测试多次查询的平均性能
        test_rounds = 5
        total_time = 0
        
        print(f"\n进行 {test_rounds} 轮性能测试...")
        
        for i in range(test_rounds):
            start_time = time.time()
            stats_result = CompanyDataStatsService.get_company_data_statistics()
            end_time = time.time()
            
            round_time = end_time - start_time
            total_time += round_time
            
            print(f"  第 {i+1} 轮: {round_time:.3f} 秒")
        
        average_time = total_time / test_rounds
        print(f"\n📊 性能测试结果:")
        print(f"  - 总耗时: {total_time:.3f} 秒")
        print(f"  - 平均耗时: {average_time:.3f} 秒")
        print(f"  - 最大可接受耗时: 2.000 秒")
        
        if average_time < 2.0:
            print("✅ 性能测试通过，查询速度满足要求")
            return True
        else:
            print("⚠️  性能测试警告，查询速度较慢，建议优化")
            return False
            
    except Exception as e:
        print(f"性能测试失败: {str(e)}")
        return False
    finally:
        try:
            SQLUtil.close()
        except:
            pass


def test_sql_conditions():
    """测试 SQL 条件的正确性"""
    print("\n" + "=" * 60)
    print("测试 SQL 条件的正确性")
    print("=" * 60)
    
    try:
        SQLUtil.connect()
        
        # 1. 测试上市公司条件
        print("\n1. 测试上市公司筛选条件...")
        
        # 查询一些样本数据来验证条件
        sample_companies = SQLUtil.query_by_column(
            CompanyMain,
            "status",
            1,
            exact_match=True,
            limit=10
        )
        
        print(f"  - 样本数据量: {len(sample_companies)}")
        
        for i, company in enumerate(sample_companies[:5]):
            stock_abbr = company.StockAbbr
            is_listed = (stock_abbr and 
                        stock_abbr.strip() and 
                        stock_abbr.strip().upper() != 'NULL')
            
            print(f"  - 样本 {i+1}: {company.CompanyCode}, StockAbbr='{stock_abbr}', 是否上市={is_listed}")
        
        # 2. 测试今日更新条件
        print("\n2. 测试今日更新筛选条件...")
        
        today = date.today()
        today_start = datetime.combine(today, datetime.min.time())
        
        print(f"  - 今日起始时间: {today_start}")
        
        # 查询最近更新的几条记录
        recent_companies = SQLUtil.query_by_column(
            CompanyMain,
            "status",
            1,
            exact_match=True,
            limit=5,
            order_by="update_time.desc()"
        )
        
        for i, company in enumerate(recent_companies):
            is_today = company.update_time >= today_start
            print(f"  - 最近更新 {i+1}: {company.CompanyCode}, 更新时间={company.update_time}, 是否今日更新={is_today}")
        
        return True
        
    except Exception as e:
        print(f"SQL 条件测试失败: {str(e)}")
        return False
    finally:
        try:
            SQLUtil.close()
        except:
            pass


def main():
    """主测试函数"""
    print("SQL 优化测试工具")
    print("测试将验证 SQL 查询的正确性和性能")
    
    tests = [
        ("SQL 优化正确性测试", test_sql_optimization),
        ("SQL 查询性能测试", test_sql_performance),
        ("SQL 条件正确性测试", test_sql_conditions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 40)
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 60)
    
    if passed == total:
        print("🎉 所有测试通过！SQL 优化成功")
        print("\n✅ 优化成果:")
        print("- 使用 SQL 条件查询替代内存过滤")
        print("- 提高查询性能，减少内存占用")
        print("- 保持统计结果的准确性")
        print("- 支持大数据量的高效统计")
    else:
        print("⚠️  部分测试失败，需要进一步优化")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
