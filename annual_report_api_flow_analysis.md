# annual_report_api 程序运行流程分析

## 概述

`annual_report_api` 是一个年报信息提取接口，主要功能是从年报文档中提取公司的关联企业信息。该接口集成了向量检索、大模型分析、数据结构化等多种技术。

## 完整流程图

```mermaid
flowchart TD
    %% 主流程开始
    A["🚀 开始: annual_report_info_ext<br/>📁 api/routes/annual_report_api.py<br/>🔧 annual_report_info_ext()<br/>📥 入参: AnnualReportEntity{company_name:str}<br/>📤 出参: SuccessResponse{code:int, message:str, data:dict} | FalseResponse{code:int, message:str, data:dict}<br/>🔗 调用: AnnualReportService.common_build_ext()"] --> B["📝 PDF文件名映射<br/>📁 api/routes/annual_report_api.py<br/>🔧 pdf_name_to_filename.get()<br/>📥 入参: company_name:str<br/>📤 出参: pdf_filename:str<br/>🔗 调用: 无"]

    B --> C["🔄 通用构建提取<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportService.common_build_ext()<br/>📥 入参: company_name:str, pdf_filename:str, year:str='2023', industry_chain:str='工业机器人', collection:str<br/>📤 出参: annual_report_data:dict<br/>🔗 调用: MongodbUtil.query_docs_by_condition(), AnnualReportCompanyAnalysis()"]

    C --> D{"🔍 检查历史缓存<br/>📁 service/annual_report_service.py<br/>🔧 MongodbUtil.query_docs_by_condition()<br/>🗄️ 表: ANNUAL_REPORT_HISTORY<br/>📥 入参: collection_name:str, search_condition:dict{company_name, year}<br/>📤 出参: result:list<br/>🔗 调用: MongodbUtil.connect()"}

    D -->|有缓存数据| E["✅ 返回缓存结果<br/>📁 service/annual_report_service.py<br/>🔧 return result[0]<br/>📥 入参: cached_data:dict<br/>📤 出参: cached_result:dict<br/>🔗 调用: 无"]

    D -->|无缓存数据| F["🔧 初始化分析工具<br/>📁 service/annual_report_service.py<br/>🔧 实例化分析工具<br/>📥 入参: collection_name:str<br/>📤 出参: milvus_util, embutil, com_analysis<br/>🔗 调用: MilvusUtil(), TextEmbedService(), AnnualReportCompanyAnalysis()"]

    %% 关联公司分析子流程
    F --> G["👨‍👩‍👧‍👦 母公司分析<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_parentand()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: parentand_result:list[str]<br/>🔗 调用: text_embedding(), search_by_vector(), answer_question()"]

    G --> H["👶 子公司分析<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_subsidiary()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: subsidiary_result:list[str]<br/>🔗 调用: text_embedding(), search_by_vector(), answer_question()"]

    H --> I["🤝 合营联营企业分析<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_cooperative_enterprise()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: cooperative_result:list[str]<br/>🔗 调用: text_embedding(), search_by_vector(), answer_question()"]

    I --> J["👥 其他关联方分析<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_other_related()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: other_related_result:list[str]<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"]

    J --> K["🛒 采购商品分析<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_purchase_goods()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: purchase_result:list[str]<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"]

    K --> L["💰 出售商品分析<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_sell_goods()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: sell_result:list[str]<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"]

    L --> M["🏠 租赁分析<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_lessor()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: lessor_result:list[str]<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"]

    M --> N["🛡️ 担保分析<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_guarantee()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: guarantee_result:list[str]<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"]

    N --> O["🛡️ 被担保分析<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_guaranteed()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: guaranteed_result:list[str]<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"]

    %% RAG检索子流程
    G --> G1["🔍 RAG检索<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.common_rag()<br/>🗄️ 表: 指定Milvus集合<br/>📥 入参: query:str, pdf_filename:str, keywords:list[str]<br/>📤 出参: documents:str, is_hit:bool<br/>🔗 调用: text_embedding(), search_by_vector(), rerank()"]

    G1 --> G2["🤖 大模型分析<br/>📁 service/annual_report_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict{role:str, content:str}], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]

    %% 数据结构化
    O --> P["📊 数据结构化<br/>📁 service/annual_report_service.py<br/>🔧 构建results_list和final_list<br/>📥 入参: 所有分析结果:dict<br/>📤 出参: structured_data:dict{results_list, final_list}<br/>🔗 调用: 数据整合方法"]

    P --> Q["🆔 生成UUID<br/>📁 service/annual_report_service.py<br/>🔧 uuid.uuid1().hex<br/>📥 入参: 无<br/>📤 出参: unique_id:str<br/>🔗 调用: uuid.uuid1()"]

    Q --> R["🔍 检查历史数据<br/>📁 service/annual_report_service.py<br/>🔧 MongodbUtil.query_docs_by_condition()<br/>🗄️ 表: ANNUAL_REPORT_HISTORY<br/>📥 入参: collection_name:str, search_condition:dict{company_name, year}<br/>📤 出参: result:list<br/>🔗 调用: MongodbUtil.connect()"]

    R -->|无历史数据| S["💾 保存结果<br/>📁 service/annual_report_service.py<br/>🔧 MongodbUtil.insert_one()<br/>🗄️ 表: ANNUAL_REPORT_HISTORY<br/>📥 入参: collection_name:str, doc_content:dict{_id, company_name, year, 公告url, 公司数量, 年报关联公司汇总, 待入库具体信息}<br/>📤 出参: insert_result<br/>🔗 调用: MongodbUtil.coll().insert_one()"]

    R -->|有历史数据| T["📝 跳过保存<br/>📁 service/annual_report_service.py<br/>🔧 print('has data')<br/>📥 入参: 无<br/>📤 出参: 无<br/>🔗 调用: 无"]

    S --> U["✅ 返回成功响应<br/>📁 service/annual_report_service.py<br/>🔧 return save_data<br/>📥 入参: data:dict<br/>📤 出参: save_data:dict<br/>🔗 调用: 无"]

    T --> U

    %% 异常处理
    A --> V["⚠️ 异常捕获<br/>📁 api/routes/annual_report_api.py<br/>🔧 try-except块<br/>📥 入参: Exception<br/>📤 出参: error_detail:str<br/>🔗 调用: 无"]
    V --> W["❌ 错误响应<br/>📁 entity/annual_report_entity.py<br/>🔧 FalseResponse()<br/>📥 入参: data:dict{error:str}<br/>📤 出参: FalseResponse{code:500, message:'error', data:dict}<br/>🔗 调用: 无"]

    E --> Z[🏁 结束]
    U --> Z
    W --> Z

    %% 样式定义
    style A fill:#e1f5fe
    style Z fill:#e8f5e8
    style C fill:#fff3e0
    style G fill:#fff9c4
    style H fill:#f3e5f5
    style I fill:#e8f5e8
    style J fill:#fff3e0
    style K fill:#f3e5f5
    style V fill:#ffebee
    style W fill:#ffcdd2
```

## 母公司分析详细流程图 (直接向量检索)

```mermaid
flowchart TD
    A["👨‍👩‍👧‍👦 母公司分析开始<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_parentand()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: parentand_result:dict<br/>🔗 调用: text_embedding(), search_by_vector(), answer_question()"] --> B["❓ 构建查询问题<br/>📁 service/annual_report_service.py<br/>🔧 构建query字符串<br/>📥 入参: company_name:str<br/>📤 出参: query:str='{company_name}公司在 **本企业的母公司情况**查找母公司'<br/>🔗 调用: 无"]

    B --> C["🧮 文本向量化<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: query:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]

    C --> D["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ 向量库: 指定Milvus集合<br/>📥 入参: collection_name:str, vector:list[float], limit:int=8, expr:str='file_title == pdf_filename'<br/>📤 出参: docs:list[dict]<br/>🔗 调用: milvus_client.search()"]

    D --> E["📄 提取文档内容<br/>📁 service/annual_report_service.py<br/>🔧 提取chunk_content_father<br/>📥 入参: docs:list[dict]<br/>📤 出参: chunk_contents:list[str], documents:str<br/>🔗 调用: 字符串拼接"]

    E --> F["🤖 大模型分析<br/>📁 service/annual_report_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]

    F --> G["📝 解析结果<br/>📁 service/annual_report_service.py<br/>🔧 解析LLM输出<br/>📥 入参: response:str<br/>📤 出参: parentand_dict:dict{母公司:list[str]}<br/>🔗 调用: 字符串处理方法"]

    G --> H["✅ 返回母公司结果<br/>📁 service/annual_report_service.py<br/>🔧 return parentand_dict<br/>📥 入参: 完整数据:dict<br/>📤 出参: parentand_dict:dict<br/>🔗 调用: 无"]

    style A fill:#e1f5fe
    style C fill:#fff9c4
    style D fill:#e8f5e8
    style F fill:#f3e5f5
    style H fill:#e8f5e8
```

## 子公司分析详细流程图 (直接向量检索)

```mermaid
flowchart TD
    A["👶 子公司分析开始<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_subsidiary()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: subsidiary_result:dict<br/>🔗 调用: text_embedding(), search_by_vector(), answer_question()"] --> B["❓ 构建查询问题<br/>📁 service/annual_report_service.py<br/>🔧 构建query字符串<br/>📥 入参: company_name:str<br/>📤 出参: query:str='{company_name}的主要子公司情况'<br/>🔗 调用: 无"]

    B --> C["🧮 文本向量化<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: query:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]

    C --> D["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ 向量库: 指定Milvus集合<br/>📥 入参: collection_name:str, vector:list[float], limit:int=8, expr:str='file_title == pdf_filename'<br/>📤 出参: docs:list[dict]<br/>🔗 调用: milvus_client.search()"]

    D --> E["📄 提取文档内容<br/>📁 service/annual_report_service.py<br/>🔧 提取chunk_content_father<br/>📥 入参: docs:list[dict]<br/>📤 出参: chunk_contents:list[str], documents:str<br/>🔗 调用: 字符串拼接"]

    E --> F["🤖 大模型分析<br/>📁 service/annual_report_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]

    F --> G["📝 解析结果<br/>📁 service/annual_report_service.py<br/>🔧 解析LLM输出<br/>📥 入参: response:str<br/>📤 出参: subsidiary_dict:dict{子公司:list[str]}<br/>🔗 调用: 字符串处理方法"]

    G --> H["✅ 返回子公司结果<br/>📁 service/annual_report_service.py<br/>🔧 return subsidiary_dict<br/>📥 入参: 完整数据:dict<br/>📤 出参: subsidiary_dict:dict<br/>🔗 调用: 无"]

    style A fill:#e1f5fe
    style C fill:#fff9c4
    style D fill:#e8f5e8
    style F fill:#f3e5f5
    style H fill:#e8f5e8
```

## 合营联营企业分析详细流程图 (直接向量检索)

```mermaid
flowchart TD
    A["🤝 合营联营企业分析开始<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_cooperative_enterprise()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: cooperative_result:dict<br/>🔗 调用: text_embedding(), search_by_vector(), answer_question()"] --> B["❓ 构建查询问题<br/>📁 service/annual_report_service.py<br/>🔧 构建query字符串<br/>📥 入参: company_name:str<br/>📤 出参: query:str='{company_name}的主要联营企业和合营企业情况'<br/>🔗 调用: 无"]

    B --> C["🧮 文本向量化<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: query:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]

    C --> D["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ 向量库: 指定Milvus集合<br/>📥 入参: collection_name:str, vector:list[float], limit:int=8, expr:str='file_title == pdf_filename'<br/>📤 出参: docs:list[dict]<br/>🔗 调用: milvus_client.search()"]

    D --> E["📄 提取文档内容<br/>📁 service/annual_report_service.py<br/>🔧 提取chunk_content_father<br/>📥 入参: docs:list[dict]<br/>📤 出参: chunk_contents:list[str], documents:str<br/>🔗 调用: 字符串拼接"]

    E --> F["🤖 大模型分析<br/>📁 service/annual_report_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]

    F --> G["📝 解析结果<br/>📁 service/annual_report_service.py<br/>🔧 解析LLM输出<br/>📥 入参: response:str<br/>📤 出参: cooperative_dict:dict{合营企业:list[str], 联营企业:list[str]}<br/>🔗 调用: 字符串处理方法"]

    G --> H["✅ 返回合营联营结果<br/>📁 service/annual_report_service.py<br/>🔧 return cooperative_dict<br/>📥 入参: 完整数据:dict<br/>📤 出参: cooperative_dict:dict<br/>🔗 调用: 无"]

    style A fill:#e1f5fe
    style C fill:#fff9c4
    style D fill:#e8f5e8
    style F fill:#f3e5f5
    style H fill:#e8f5e8
```

## 其他关联方分析详细流程图 (使用common_rag)

```mermaid
flowchart TD
    A["👥 其他关联方分析开始<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_other_related()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: other_related_result:dict<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"] --> B["❓ 构建查询问题<br/>📁 service/annual_report_service.py<br/>🔧 构建query字符串<br/>📥 入参: company_name:str<br/>📤 出参: query:str='{company_name}的其他关联方'<br/>🔗 调用: 无"]

    B --> C["🧮 文本向量化<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: query:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]

    C --> D["📚 common_rag检索<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.common_rag()<br/>🗄️ 表: 指定Milvus集合<br/>📥 入参: query:str, pdf_filename:str, keywords:list[str]=['其他关联方']<br/>📤 出参: documents:str, is_hit:bool<br/>🔗 调用: search_by_vector(), rerank()"]

    D --> E["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ 向量库: 指定Milvus集合<br/>📥 入参: collection_name:str, vector:list[float], limit:int, filter:str<br/>📤 出参: search_results:list[dict]<br/>🔗 调用: milvus_client.search()"]

    E --> F["🔄 重排序<br/>📁 utils/reranker_util.py<br/>🔧 QueryReranker.rerank()<br/>📥 入参: query:str, documents:list, top_k:int<br/>📤 出参: reranked_docs:list<br/>🔗 调用: reranker模型"]

    F --> G["🤖 大模型分析<br/>📁 service/annual_report_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]

    G --> H["📝 解析结果<br/>📁 service/annual_report_service.py<br/>🔧 解析LLM输出<br/>📥 入参: response:str<br/>📤 出参: other_related_dict:dict{其他关联方:list[str]}<br/>🔗 调用: 字符串处理方法"]

    H --> I["✅ 返回其他关联方结果<br/>📁 service/annual_report_service.py<br/>🔧 return other_related_dict<br/>📥 入参: 完整数据:dict<br/>📤 出参: other_related_dict:dict<br/>🔗 调用: 无"]

    style A fill:#e1f5fe
    style D fill:#fff9c4
    style E fill:#e8f5e8
    style G fill:#f3e5f5
    style I fill:#e8f5e8
```

## 采购商品分析详细流程图 (使用common_rag)

```mermaid
flowchart TD
    A["🛒 采购商品分析开始<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_purchase_goods()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: purchase_result:dict<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"] --> B["❓ 构建查询问题<br/>📁 service/annual_report_service.py<br/>🔧 构建query字符串<br/>📥 入参: company_name:str<br/>📤 出参: query:str='{company_name}采购商品或接受劳务的关联交易'<br/>🔗 调用: 无"]

    B --> C["🧮 文本向量化<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: query:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]

    C --> D["📚 common_rag检索<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.common_rag()<br/>🗄️ 表: 指定Milvus集合<br/>📥 入参: query:str, pdf_filename:str, keywords:list[str]=['采购商品', '接受劳务']<br/>📤 出参: documents:str, is_hit:bool<br/>🔗 调用: search_by_vector(), rerank()"]

    D --> E["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ 向量库: 指定Milvus集合<br/>📥 入参: collection_name:str, vector:list[float], limit:int, filter:str<br/>📤 出参: search_results:list[dict]<br/>🔗 调用: milvus_client.search()"]

    E --> F["🔄 重排序<br/>📁 utils/reranker_util.py<br/>🔧 QueryReranker.rerank()<br/>📥 入参: query:str, documents:list, top_k:int<br/>📤 出参: reranked_docs:list<br/>🔗 调用: reranker模型"]

    F --> G["🤖 大模型分析<br/>📁 service/annual_report_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]

    G --> H["📝 解析结果<br/>📁 service/annual_report_service.py<br/>🔧 解析LLM输出<br/>📥 入参: response:str<br/>📤 出参: purchase_dict:dict{采购商品或接受劳务:list[str]}<br/>🔗 调用: 字符串处理方法"]

    H --> I["✅ 返回采购商品结果<br/>📁 service/annual_report_service.py<br/>🔧 return purchase_dict<br/>📥 入参: 完整数据:dict<br/>📤 出参: purchase_dict:dict<br/>🔗 调用: 无"]

    style A fill:#e1f5fe
    style D fill:#fff9c4
    style E fill:#e8f5e8
    style G fill:#f3e5f5
    style I fill:#e8f5e8
```

## 出售商品分析详细流程图 (使用common_rag)

```mermaid
flowchart TD
    A["💰 出售商品分析开始<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_sell_goods()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: sell_result:dict<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"] --> B["❓ 构建查询问题<br/>📁 service/annual_report_service.py<br/>🔧 构建query字符串<br/>📥 入参: company_name:str<br/>📤 出参: query:str='{company_name}出售商品或提供劳务的关联交易'<br/>🔗 调用: 无"]

    B --> C["🧮 文本向量化<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: query:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]

    C --> D["📚 common_rag检索<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.common_rag()<br/>🗄️ 表: 指定Milvus集合<br/>📥 入参: query:str, pdf_filename:str, keywords:list[str]=['出售商品', '提供劳务']<br/>📤 出参: documents:str, is_hit:bool<br/>🔗 调用: search_by_vector(), rerank()"]

    D --> E["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ 向量库: 指定Milvus集合<br/>📥 入参: collection_name:str, vector:list[float], limit:int, filter:str<br/>📤 出参: search_results:list[dict]<br/>🔗 调用: milvus_client.search()"]

    E --> F["🔄 重排序<br/>📁 utils/reranker_util.py<br/>🔧 QueryReranker.rerank()<br/>📥 入参: query:str, documents:list, top_k:int<br/>📤 出参: reranked_docs:list<br/>🔗 调用: reranker模型"]

    F --> G["🤖 大模型分析<br/>📁 service/annual_report_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]

    G --> H["📝 解析结果<br/>📁 service/annual_report_service.py<br/>🔧 解析LLM输出<br/>📥 入参: response:str<br/>📤 出参: sell_dict:dict{出售商品或提供劳务:list[str]}<br/>🔗 调用: 字符串处理方法"]

    H --> I["✅ 返回出售商品结果<br/>📁 service/annual_report_service.py<br/>🔧 return sell_dict<br/>📥 入参: 完整数据:dict<br/>📤 出参: sell_dict:dict<br/>🔗 调用: 无"]

    style A fill:#e1f5fe
    style D fill:#fff9c4
    style E fill:#e8f5e8
    style G fill:#f3e5f5
    style I fill:#e8f5e8
```

## 租赁分析详细流程图 (使用common_rag)

```mermaid
flowchart TD
    A["🏠 租赁分析开始<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_lessor()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: lessor_result:dict<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"] --> B["❓ 构建查询问题<br/>📁 service/annual_report_service.py<br/>🔧 构建query字符串<br/>📥 入参: company_name:str<br/>📤 出参: query:str='{company_name}租赁关联交易'<br/>🔗 调用: 无"]

    B --> C["🧮 文本向量化<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: query:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]

    C --> D["📚 common_rag检索<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.common_rag()<br/>🗄️ 表: 指定Milvus集合<br/>📥 入参: query:str, pdf_filename:str, keywords:list[str]=['租赁']<br/>📤 出参: documents:str, is_hit:bool<br/>🔗 调用: search_by_vector(), rerank()"]

    D --> E["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ 向量库: 指定Milvus集合<br/>📥 入参: collection_name:str, vector:list[float], limit:int, filter:str<br/>📤 出参: search_results:list[dict]<br/>🔗 调用: milvus_client.search()"]

    E --> F["🔄 重排序<br/>📁 utils/reranker_util.py<br/>🔧 QueryReranker.rerank()<br/>📥 入参: query:str, documents:list, top_k:int<br/>📤 出参: reranked_docs:list<br/>🔗 调用: reranker模型"]

    F --> G["🤖 大模型分析<br/>📁 service/annual_report_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]

    G --> H["📝 解析结果<br/>📁 service/annual_report_service.py<br/>🔧 解析LLM输出<br/>📥 入参: response:str<br/>📤 出参: lessor_dict:dict{租赁:list[str]}<br/>🔗 调用: 字符串处理方法"]

    H --> I["✅ 返回租赁结果<br/>📁 service/annual_report_service.py<br/>🔧 return lessor_dict<br/>📥 入参: 完整数据:dict<br/>📤 出参: lessor_dict:dict<br/>🔗 调用: 无"]

    style A fill:#e1f5fe
    style D fill:#fff9c4
    style E fill:#e8f5e8
    style G fill:#f3e5f5
    style I fill:#e8f5e8
```

## 担保分析详细流程图 (使用common_rag)

```mermaid
flowchart TD
    A["🛡️ 担保分析开始<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_guarantee()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: guarantee_result:dict<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"] --> B["❓ 构建查询问题<br/>📁 service/annual_report_service.py<br/>🔧 构建query字符串<br/>📥 入参: company_name:str<br/>📤 出参: query:str='{company_name}担保关联交易'<br/>🔗 调用: 无"]

    B --> C["🧮 文本向量化<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: query:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]

    C --> D["📚 common_rag检索<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.common_rag()<br/>🗄️ 表: 指定Milvus集合<br/>📥 入参: query:str, pdf_filename:str, keywords:list[str]=['担保']<br/>📤 出参: documents:str, is_hit:bool<br/>🔗 调用: search_by_vector(), rerank()"]

    D --> E["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ 向量库: 指定Milvus集合<br/>📥 入参: collection_name:str, vector:list[float], limit:int, filter:str<br/>📤 出参: search_results:list[dict]<br/>🔗 调用: milvus_client.search()"]

    E --> F["🔄 重排序<br/>📁 utils/reranker_util.py<br/>🔧 QueryReranker.rerank()<br/>📥 入参: query:str, documents:list, top_k:int<br/>📤 出参: reranked_docs:list<br/>🔗 调用: reranker模型"]

    F --> G["🤖 大模型分析<br/>📁 service/annual_report_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]

    G --> H["📝 解析结果<br/>📁 service/annual_report_service.py<br/>🔧 解析LLM输出<br/>📥 入参: response:str<br/>📤 出参: guarantee_dict:dict{担保:list[str]}<br/>🔗 调用: 字符串处理方法"]

    H --> I["✅ 返回担保结果<br/>📁 service/annual_report_service.py<br/>🔧 return guarantee_dict<br/>📥 入参: 完整数据:dict<br/>📤 出参: guarantee_dict:dict<br/>🔗 调用: 无"]

    style A fill:#e1f5fe
    style D fill:#fff9c4
    style E fill:#e8f5e8
    style G fill:#f3e5f5
    style I fill:#e8f5e8
```

## 被担保分析详细流程图 (使用common_rag)

```mermaid
flowchart TD
    A["🛡️ 被担保分析开始<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.company_guaranteed()<br/>📥 入参: company_name:str, pdf_filename:str<br/>📤 出参: guaranteed_result:dict<br/>🔗 调用: text_embedding(), common_rag(), answer_question()"] --> B["❓ 构建查询问题<br/>📁 service/annual_report_service.py<br/>🔧 构建query字符串<br/>📥 入参: company_name:str<br/>📤 出参: query:str='{company_name}被担保关联交易'<br/>🔗 调用: 无"]

    B --> C["🧮 文本向量化<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: query:list[str]<br/>📤 出参: queryemb:list[float]<br/>🔗 调用: 向量化模型API"]

    C --> D["📚 common_rag检索<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportCompanyAnalysis.common_rag()<br/>🗄️ 表: 指定Milvus集合<br/>📥 入参: query:str, pdf_filename:str, keywords:list[str]=['被担保']<br/>📤 出参: documents:str, is_hit:bool<br/>🔗 调用: search_by_vector(), rerank()"]

    D --> E["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ 向量库: 指定Milvus集合<br/>📥 入参: collection_name:str, vector:list[float], limit:int, filter:str<br/>📤 出参: search_results:list[dict]<br/>🔗 调用: milvus_client.search()"]

    E --> F["🔄 重排序<br/>📁 utils/reranker_util.py<br/>🔧 QueryReranker.rerank()<br/>📥 入参: query:str, documents:list, top_k:int<br/>📤 出参: reranked_docs:list<br/>🔗 调用: reranker模型"]

    F --> G["🤖 大模型分析<br/>📁 service/annual_report_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]

    G --> H["📝 解析结果<br/>📁 service/annual_report_service.py<br/>🔧 解析LLM输出<br/>📥 入参: response:str<br/>📤 出参: guaranteed_dict:dict{被担保:list[str]}<br/>🔗 调用: 字符串处理方法"]

    H --> I["✅ 返回被担保结果<br/>📁 service/annual_report_service.py<br/>🔧 return guaranteed_dict<br/>📥 入参: 完整数据:dict<br/>📤 出参: guaranteed_dict:dict<br/>🔗 调用: 无"]

    style A fill:#e1f5fe
    style D fill:#fff9c4
    style E fill:#e8f5e8
    style G fill:#f3e5f5
    style I fill:#e8f5e8
```

## 核心接口详细说明

### 主要文件和接口调用关系

#### 1. **api/routes/annual_report_api.py**

**annual_report_info_ext 主接口**
- **调用的其他接口**:
  - `pdf_name_to_filename.get()` - PDF文件名映射
  - `AnnualReportService.common_build_ext()` - 通用构建提取
- **入参**: `AnnualReportEntity{company_name:str}`
- **出参**: `SuccessResponse{code:200, message:'success', data:dict}` 或 `FalseResponse{code:500, message:'error', data:dict}`
- **查询表**: `ANNUAL_REPORT_HISTORY`

#### 2. **service/annual_report_service.py**

**AnnualReportService.common_build_ext 通用构建提取**
- **调用的其他接口**:
  - `MongodbUtil.query_docs_by_condition()` - 历史数据查询
  - `MilvusUtil()` - Milvus工具实例化
  - `TextEmbedService()` - 文本嵌入服务实例化
  - `AnnualReportCompanyAnalysis()` - 年报公司分析实例化
  - `company_parentand()` - 母公司分析
  - `company_subsidiary()` - 子公司分析
  - `company_joint_venture()` - 合营企业分析
  - `company_associate()` - 联营企业分析
  - `company_purchase_goods()` - 采购商品分析
  - `company_sell_goods()` - 出售商品分析
  - `company_lease()` - 租赁分析
  - `company_guarantee()` - 担保分析
  - `MongodbUtil.insert_one()` - 结果保存
- **入参**: `{company_name:str, pdf_filename:str, year:str, industry_chain:str, collection:str, mongodb_id:str, is_cached:bool}`
- **出参**: `{年报关联公司分析数据:dict}`
- **查询表**: `ANNUAL_REPORT_HISTORY`, `指定Milvus集合`

**AnnualReportCompanyAnalysis.common_rag RAG检索**
- **调用的其他接口**:
  - `TextEmbedService.text_embedding()` - 文本向量化
  - `MilvusUtil.search_by_vector()` - 向量检索
  - `QueryReranker.rerank()` - 重排序
- **入参**: `{query:str, pdf_filename:str, keywords:list[str]}`
- **出参**: `{documents:str, is_hit:bool}`
- **查询表**: `指定Milvus集合`

**company_parentand 母公司分析**
- **调用的其他接口**:
  - `TextEmbedService.text_embedding()` - 文本向量化
  - `MilvusUtil.search_by_vector()` - 向量检索
  - `Llm_Service.answer_question()` - 大模型问答
- **入参**: `{company_name:str, pdf_filename:str}`
- **出参**: `{parentand_result:list[str]}`
- **查询表**: `指定Milvus集合`

**company_subsidiary 子公司分析**
- **调用的其他接口**:
  - `TextEmbedService.text_embedding()` - 文本向量化
  - `MilvusUtil.search_by_vector()` - 向量检索
  - `Llm_Service.answer_question()` - 大模型问答
- **入参**: `{company_name:str, pdf_filename:str}`
- **出参**: `{subsidiary_result:list[str]}`
- **查询表**: `指定Milvus集合`

**company_cooperative_enterprise 合营联营企业分析**
- **调用的其他接口**:
  - `TextEmbedService.text_embedding()` - 文本向量化
  - `MilvusUtil.search_by_vector()` - 向量检索
  - `Llm_Service.answer_question()` - 大模型问答
- **入参**: `{company_name:str, pdf_filename:str}`
- **出参**: `{cooperative_result:list[str]}`
- **查询表**: `指定Milvus集合`

**company_other_related 其他关联方分析**
- **调用的其他接口**:
  - `TextEmbedService.text_embedding()` - 文本向量化
  - `common_rag()` - RAG检索
  - `Llm_Service.answer_question()` - 大模型问答
- **入参**: `{company_name:str, pdf_filename:str}`
- **出参**: `{other_related_result:list[str]}`
- **查询表**: `指定Milvus集合`

**company_purchase_goods 采购商品分析**
- **调用的其他接口**:
  - `TextEmbedService.text_embedding()` - 文本向量化
  - `common_rag()` - RAG检索
  - `Llm_Service.answer_question()` - 大模型问答
- **入参**: `{company_name:str, pdf_filename:str}`
- **出参**: `{purchase_result:list[str]}`
- **查询表**: `指定Milvus集合`

**company_sell_goods 出售商品分析**
- **调用的其他接口**:
  - `TextEmbedService.text_embedding()` - 文本向量化
  - `common_rag()` - RAG检索
  - `Llm_Service.answer_question()` - 大模型问答
- **入参**: `{company_name:str, pdf_filename:str}`
- **出参**: `{sell_result:list[str]}`
- **查询表**: `指定Milvus集合`

**company_lessor 租赁分析**
- **调用的其他接口**:
  - `TextEmbedService.text_embedding()` - 文本向量化
  - `common_rag()` - RAG检索
  - `Llm_Service.answer_question()` - 大模型问答
- **入参**: `{company_name:str, pdf_filename:str}`
- **出参**: `{lessor_result:list[str]}`
- **查询表**: `指定Milvus集合`

**company_guarantee 担保分析**
- **调用的其他接口**:
  - `TextEmbedService.text_embedding()` - 文本向量化
  - `common_rag()` - RAG检索
  - `Llm_Service.answer_question()` - 大模型问答
- **入参**: `{company_name:str, pdf_filename:str}`
- **出参**: `{guarantee_result:list[str]}`
- **查询表**: `指定Milvus集合`

**company_guaranteed 被担保分析**
- **调用的其他接口**:
  - `TextEmbedService.text_embedding()` - 文本向量化
  - `common_rag()` - RAG检索
  - `Llm_Service.answer_question()` - 大模型问答
- **入参**: `{company_name:str, pdf_filename:str}`
- **出参**: `{guaranteed_result:list[str]}`
- **查询表**: `指定Milvus集合`

#### 3. **utils/milvus_util.py**

**MilvusUtil.search_by_vector 向量检索**
- **调用的其他接口**:
  - `milvus_client.search()` - Milvus客户端
- **入参**: `{collection_name:str, vector:list[float], limit:int, filter:str}`
- **出参**: `{search_results:list[dict]}`

#### 4. **utils/reranker_util.py**

**QueryReranker.rerank 重排序**
- **调用的其他接口**:
  - `reranker模型` - 重排序模型
- **入参**: `{query:str, documents:list, top_k:int}`
- **出参**: `{reranked_docs:list}`

## 数据表操作详细信息

### MongoDB 集合操作

| 集合名称 | 操作类型 | 接口方法 | 查询条件 | 数据结构 | 用途说明 |
|---------|---------|----------|----------|----------|----------|
| ANNUAL_REPORT_HISTORY | 查询 | query_docs_by_condition() | `{company_name:str, year:str}` | 历史分析结果 | 缓存查询，避免重复分析 |
| ANNUAL_REPORT_HISTORY | 插入 | insert_one() | 无 | 年报关联公司分析结果 | 保存分析结果 |

### Milvus 向量库操作

| 集合名称 | 操作类型 | 接口方法 | 查询条件 | 数据结构 | 用途说明 |
|---------|---------|----------|----------|----------|----------|
| 指定Milvus集合 | 检索 | search_by_vector() | `{vector, limit, filter}` | 相似文本块 | 语义相似度检索 |

## 关键功能模块详细说明

### 1. 历史数据缓存机制
- **缓存键**: 基于公司名称和年份 `{company_name, year}`
- **缓存存储**: MongoDB `ANNUAL_REPORT_HISTORY` 集合
- **缓存查询**: `MongodbUtil.query_docs_by_condition()` 方法
- **缓存数据结构**:
```python
{
    "_id": str,                      # 唯一标识
    "company_name": str,             # 公司名称
    "year": str,                     # 年份
    "公告url": str,                  # 年报文件URL
    "公司数量": int,                 # 关联公司总数
    "年报关联公司汇总": list,        # 关联公司汇总信息
    "待入库具体信息": list,          # 详细关联公司信息
    "file_time": str,               # 文件时间
    "source_id": str                # 来源ID
}
```

### 2. 关联公司分析
- **多维度分析**: 母公司、子公司、合营企业、联营企业、采购商品、出售商品、租赁、担保等8个维度
- **RAG检索**: 基于关键词和向量检索的混合检索方式
- **重排序优化**: 使用重排序模型提高检索结果质量

### 3. RAG检索和信息提取
- **检索流程**: 问题向量化 → Milvus检索 → 重排序 → 相关文档整合
- **信息提取**: 基于检索到的相关文档，使用大模型提取结构化信息
- **关键词匹配**: 结合关键词匹配提高检索准确性

### 4. 数据结构化和保存
- **结构化**: 将提取的信息整合为标准化数据结构
- **去重保存**: 检查历史数据避免重复保存
- **结果保存**: 将结构化数据保存到MongoDB

## 技术特点

1. **预定义文件映射**: 支持从预定义的公司名称映射到对应的年报文件
2. **多维度关联分析**: 母公司、子公司、合营企业、联营企业、采购、出售、租赁、担保等8个维度的全面分析
3. **RAG增强检索**: 基于向量检索和关键词匹配的精准信息提取，提高准确性
4. **智能缓存**: 基于公司名称和年份的缓存机制，避免重复处理
5. **重排序优化**: 使用重排序模型提高检索结果质量
6. **结构化输出**: 标准化的年报关联公司数据结构，便于后续处理
7. **异常处理**: 完善的错误处理和日志记录机制
8. **灵活配置**: 支持自定义Milvus集合和缓存控制
