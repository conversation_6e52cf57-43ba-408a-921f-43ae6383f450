# annual_report_api 程序运行流程分析

## 概述

`annual_report_api` 是一个年报信息提取接口，主要功能是从年报PDF文档中提取公司的基本信息、财务数据、业务信息等。该接口集成了PDF解析、向量检索、大模型分析、数据结构化等多种技术。

## 完整流程图

```mermaid
flowchart TD
    %% 主流程开始
    A["🚀 开始: annual_report_ext<br/>📁 api/routes/annual_report_api.py<br/>🔧 annual_report_ext()<br/>📥 入参: AnnualReportRequest{company_name:str, year:str, pdf_url:str}<br/>📤 出参: SuccessResponse{code:int, message:str, data:dict} | FalseResponse{code:int, message:str, data:dict}<br/>🔗 调用: common_build_ext(), MongodbUtil.query_docs_by_condition()"] --> B["🔍 检查历史数据<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.query_docs_by_condition()<br/>🗄️ 表: ANNUAL_REPORT_HISTORY<br/>📥 入参: collection_name:str, search_condition:dict{company_name, year}<br/>📤 出参: result:list<br/>🔗 调用: MongodbUtil.connect()"]
    
    B -->|有历史数据| C["✅ 返回历史结果<br/>📁 entity/annual_report_entity.py<br/>🔧 SuccessResponse()<br/>📥 入参: data:dict<br/>📤 出参: SuccessResponse{code:200, message:'success', data:dict}<br/>🔗 调用: 无"]
    
    B -->|无历史数据| D["🔄 通用构建提取<br/>📁 service/annual_report_service.py<br/>🔧 AnnualReportService.common_build_ext()<br/>📥 入参: company_name:str, year:str, pdf_url:str<br/>📤 出参: annual_report_data:dict<br/>🔗 调用: pdf_to_text(), build_milvus_collection(), extract_company_info()"]
    
    %% PDF处理子流程
    D --> E["📄 PDF文本提取<br/>📁 utils/pdf_util.py<br/>🔧 PdfUtil.pdf_to_text()<br/>📥 入参: pdf_url:str<br/>📤 出参: text_content:str, page_count:int<br/>🔗 调用: PyPDF2.PdfReader()"]
    
    E --> F["📝 文本分块<br/>📁 service/annual_report_service.py<br/>🔧 text_chunking()<br/>📥 入参: text_content:str, chunk_size:int=1000<br/>📤 出参: chunks:list[str]<br/>🔗 调用: 文本分割算法"]
    
    F --> G["🧮 文本向量化<br/>📁 utils/text_embed_util.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 入参: texts:list[str]<br/>📤 出参: embeddings:list[list[float]]<br/>🔗 调用: 向量化模型API"]
    
    G --> H["🗄️ 构建Milvus集合<br/>📁 service/annual_report_service.py<br/>🔧 build_milvus_collection()<br/>🗄️ 表: ANNUAL_REPORT_MILVUS_{company_name}_{year}<br/>📥 入参: collection_name:str, chunks:list, embeddings:list<br/>📤 出参: collection_created:bool<br/>🔗 调用: MilvusUtil.create_collection(), MilvusUtil.insert()"]
    
    %% 信息提取子流程
    H --> I["🏢 公司基本信息提取<br/>📁 service/annual_report_service.py<br/>🔧 extract_company_basic_info()<br/>📥 入参: collection_name:str, company_name:str<br/>📤 出参: basic_info:dict{公司全称, 注册地址, 办公地址, 法定代表人, 董事会秘书}<br/>🔗 调用: rag_search(), answer_question()"]
    
    I --> J["💰 财务信息提取<br/>📁 service/annual_report_service.py<br/>🔧 extract_financial_info()<br/>📥 入参: collection_name:str, company_name:str, year:str<br/>📤 出参: financial_info:dict{营业收入, 净利润, 总资产, 净资产, 资产负债率}<br/>🔗 调用: rag_search(), answer_question()"]
    
    J --> K["📊 业务信息提取<br/>📁 service/annual_report_service.py<br/>🔧 extract_business_info()<br/>📥 入参: collection_name:str, company_name:str<br/>📤 出参: business_info:dict{主营业务, 主要产品, 行业地位, 核心竞争力}<br/>🔗 调用: rag_search(), answer_question()"]
    
    K --> L["👥 治理信息提取<br/>📁 service/annual_report_service.py<br/>🔧 extract_governance_info()<br/>📥 入参: collection_name:str, company_name:str<br/>📤 出参: governance_info:dict{董事会成员, 监事会成员, 高级管理人员}<br/>🔗 调用: rag_search(), answer_question()"]
    
    L --> M["🔍 风险信息提取<br/>📁 service/annual_report_service.py<br/>🔧 extract_risk_info()<br/>📥 入参: collection_name:str, company_name:str<br/>📤 出参: risk_info:dict{主要风险因素, 风险应对措施}<br/>🔗 调用: rag_search(), answer_question()"]
    
    %% RAG检索子流程
    I --> I1["🔍 RAG检索<br/>📁 service/annual_report_service.py<br/>🔧 rag_search()<br/>🗄️ 表: ANNUAL_REPORT_MILVUS_{company_name}_{year}<br/>📥 入参: collection_name:str, query:str, top_k:int=5<br/>📤 出参: relevant_docs:list[str]<br/>🔗 调用: text_embedding(), search_by_vector()"]
    
    I1 --> I2["🤖 大模型分析<br/>📁 service/annual_report_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 入参: messages:list[dict{role:str, content:str}], model:str<br/>📤 出参: response:str<br/>🔗 调用: LlmModel.get_model_client()"]
    
    %% 数据结构化
    M --> N["🏗️ 数据结构化<br/>📁 service/annual_report_service.py<br/>🔧 structure_annual_data()<br/>📥 入参: basic_info, financial_info, business_info, governance_info, risk_info<br/>📤 出参: structured_data:dict<br/>🔗 调用: 数据整合方法"]
    
    N --> O["🆔 生成UUID<br/>📁 service/annual_report_service.py<br/>🔧 uuid.uuid1().hex<br/>📥 入参: 无<br/>📤 出参: unique_id:str<br/>🔗 调用: uuid.uuid1()"]
    
    O --> P["💾 保存结果<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.insert_one()<br/>🗄️ 表: ANNUAL_REPORT_HISTORY<br/>📥 入参: collection_name:str, doc_content:dict{_id, company_name, year, pdf_url, 基本信息, 财务信息, 业务信息, 治理信息, 风险信息}<br/>📤 出参: insert_result<br/>🔗 调用: MongodbUtil.coll().insert_one()"]
    
    P --> Q["🗑️ 清理临时集合<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.drop_collection()<br/>🗄️ 表: ANNUAL_REPORT_MILVUS_{company_name}_{year}<br/>📥 入参: collection_name:str<br/>📤 出参: drop_result:bool<br/>🔗 调用: milvus_client.drop_collection()"]
    
    Q --> R["✅ 返回成功响应<br/>📁 entity/annual_report_entity.py<br/>🔧 SuccessResponse()<br/>📥 入参: data:dict<br/>📤 出参: SuccessResponse{code:200, message:'success', data:dict}<br/>🔗 调用: 无"]
    
    %% 异常处理
    A --> S["⚠️ 异常捕获<br/>📁 api/routes/annual_report_api.py<br/>🔧 try-except块<br/>📥 入参: Exception<br/>📤 出参: error_detail:str<br/>🔗 调用: 无"]
    S --> T["❌ 错误响应<br/>📁 entity/annual_report_entity.py<br/>🔧 FalseResponse()<br/>📥 入参: data:dict{error:str}<br/>📤 出参: FalseResponse{code:500, message:'error', data:dict}<br/>🔗 调用: 无"]
    
    C --> Z[🏁 结束]
    R --> Z
    T --> Z
    
    %% 样式定义
    style A fill:#e1f5fe
    style Z fill:#e8f5e8
    style D fill:#fff3e0
    style I fill:#fff9c4
    style J fill:#f3e5f5
    style K fill:#e8f5e8
    style L fill:#fff3e0
    style M fill:#f3e5f5
    style S fill:#ffebee
    style T fill:#ffcdd2
```

## 核心接口详细说明

### 主要文件和接口调用关系

#### 1. **api/routes/annual_report_api.py**

**annual_report_ext 主接口**
- **调用的其他接口**:
  - `MongodbUtil.query_docs_by_condition()` - 历史数据查询
  - `AnnualReportService.common_build_ext()` - 通用构建提取
  - `MongodbUtil.insert_one()` - 结果保存
- **入参**: `AnnualReportRequest{company_name:str, year:str, pdf_url:str}`
- **出参**: `SuccessResponse{code:200, message:'success', data:dict}` 或 `FalseResponse{code:500, message:'error', data:dict}`
- **查询表**: `ANNUAL_REPORT_HISTORY`

#### 2. **service/annual_report_service.py**

**AnnualReportService.common_build_ext 通用构建提取**
- **调用的其他接口**:
  - `PdfUtil.pdf_to_text()` - PDF文本提取
  - `text_chunking()` - 文本分块
  - `TextEmbedService.text_embedding()` - 文本向量化
  - `build_milvus_collection()` - 构建Milvus集合
  - `extract_company_basic_info()` - 公司基本信息提取
  - `extract_financial_info()` - 财务信息提取
  - `extract_business_info()` - 业务信息提取
  - `extract_governance_info()` - 治理信息提取
  - `extract_risk_info()` - 风险信息提取
- **入参**: `{company_name:str, year:str, pdf_url:str}`
- **出参**: `{annual_report_data:dict}`
- **查询表**: `ANNUAL_REPORT_MILVUS_{company_name}_{year}` (临时Milvus集合)

**extract_company_basic_info 公司基本信息提取**
- **调用的其他接口**:
  - `rag_search()` - RAG检索
  - `Llm_Service.answer_question()` - 大模型问答
- **入参**: `{collection_name:str, company_name:str}`
- **出参**: `{basic_info:dict{公司全称, 注册地址, 办公地址, 法定代表人, 董事会秘书}}`
- **查询表**: `ANNUAL_REPORT_MILVUS_{company_name}_{year}`

**extract_financial_info 财务信息提取**
- **调用的其他接口**:
  - `rag_search()` - RAG检索
  - `Llm_Service.answer_question()` - 大模型问答
- **入参**: `{collection_name:str, company_name:str, year:str}`
- **出参**: `{financial_info:dict{营业收入, 净利润, 总资产, 净资产, 资产负债率}}`
- **查询表**: `ANNUAL_REPORT_MILVUS_{company_name}_{year}`

**extract_business_info 业务信息提取**
- **调用的其他接口**:
  - `rag_search()` - RAG检索
  - `Llm_Service.answer_question()` - 大模型问答
- **入参**: `{collection_name:str, company_name:str}`
- **出参**: `{business_info:dict{主营业务, 主要产品, 行业地位, 核心竞争力}}`
- **查询表**: `ANNUAL_REPORT_MILVUS_{company_name}_{year}`

**rag_search RAG检索**
- **调用的其他接口**:
  - `TextEmbedService.text_embedding()` - 文本向量化
  - `MilvusUtil.search_by_vector()` - 向量检索
- **入参**: `{collection_name:str, query:str, top_k:int=5}`
- **出参**: `{relevant_docs:list[str]}`
- **查询表**: `ANNUAL_REPORT_MILVUS_{company_name}_{year}`

#### 3. **utils/pdf_util.py**

**PdfUtil.pdf_to_text PDF文本提取**
- **调用的其他接口**:
  - `PyPDF2.PdfReader()` - PDF解析
  - `requests.get()` - 文件下载
- **入参**: `{pdf_url:str}`
- **出参**: `{text_content:str, page_count:int}`

#### 4. **utils/milvus_util.py**

**MilvusUtil.create_collection 创建集合**
- **调用的其他接口**:
  - `milvus_client.create_collection()` - Milvus客户端
- **入参**: `{collection_name:str, dimension:int}`
- **出参**: `{collection_created:bool}`

**MilvusUtil.insert 插入数据**
- **调用的其他接口**:
  - `milvus_client.insert()` - Milvus客户端
- **入参**: `{collection_name:str, data:list[dict]}`
- **出参**: `{insert_result}`

**MilvusUtil.search_by_vector 向量检索**
- **调用的其他接口**:
  - `milvus_client.search()` - Milvus客户端
- **入参**: `{collection_name:str, vector:list[float], limit:int}`
- **出参**: `{search_results:list[dict]}`

**MilvusUtil.drop_collection 删除集合**
- **调用的其他接口**:
  - `milvus_client.drop_collection()` - Milvus客户端
- **入参**: `{collection_name:str}`
- **出参**: `{drop_result:bool}`

## 数据表操作详细信息

### MongoDB 集合操作

| 集合名称 | 操作类型 | 接口方法 | 查询条件 | 数据结构 | 用途说明 |
|---------|---------|----------|----------|----------|----------|
| ANNUAL_REPORT_HISTORY | 查询 | query_docs_by_condition() | `{company_name:str, year:str}` | 历史分析结果 | 缓存查询，避免重复分析 |
| ANNUAL_REPORT_HISTORY | 插入 | insert_one() | 无 | 年报分析结果 | 保存分析结果 |

### Milvus 向量库操作

| 集合名称 | 操作类型 | 接口方法 | 查询条件 | 数据结构 | 用途说明 |
|---------|---------|----------|----------|----------|----------|
| ANNUAL_REPORT_MILVUS_{company}_{year} | 创建 | create_collection() | 无 | 临时向量集合 | 存储年报文本向量 |
| ANNUAL_REPORT_MILVUS_{company}_{year} | 插入 | insert() | 无 | 文本块向量 | 插入文本向量数据 |
| ANNUAL_REPORT_MILVUS_{company}_{year} | 检索 | search_by_vector() | `{vector, limit}` | 相似文本块 | 语义相似度检索 |
| ANNUAL_REPORT_MILVUS_{company}_{year} | 删除 | drop_collection() | 无 | 无 | 清理临时集合 |

## 关键功能模块详细说明

### 1. 历史数据缓存机制
- **缓存键**: 基于公司名称和年份 `{company_name, year}`
- **缓存存储**: MongoDB `ANNUAL_REPORT_HISTORY` 集合
- **缓存查询**: `MongodbUtil.query_docs_by_condition()` 方法
- **缓存数据结构**:
```python
{
    "_id": str,                      # 唯一标识
    "company_name": str,             # 公司名称
    "year": str,                     # 年份
    "pdf_url": str,                  # PDF文件URL
    "基本信息": dict,                # 公司基本信息
    "财务信息": dict,                # 财务数据
    "业务信息": dict,                # 业务信息
    "治理信息": dict,                # 公司治理信息
    "风险信息": dict,                # 风险因素信息
    "create_time": datetime          # 创建时间
}
```

### 2. PDF处理和向量化
- **PDF解析**: 使用PyPDF2提取文本内容
- **文本分块**: 按固定大小分割文本，便于向量化
- **向量化**: 使用文本嵌入模型生成向量
- **临时存储**: 创建临时Milvus集合存储向量数据

### 3. RAG检索和信息提取
- **检索流程**: 问题向量化 → Milvus检索 → 相关文档整合
- **信息提取**: 基于检索到的相关文档，使用大模型提取结构化信息
- **多维度提取**: 基本信息、财务信息、业务信息、治理信息、风险信息

### 4. 数据结构化和清理
- **结构化**: 将提取的信息整合为标准化数据结构
- **临时清理**: 处理完成后删除临时Milvus集合
- **结果保存**: 将结构化数据保存到MongoDB

## 技术特点

1. **PDF智能解析**: 支持从PDF URL直接提取和分析
2. **临时向量存储**: 动态创建和清理Milvus集合
3. **多维度信息提取**: 全面的年报信息提取
4. **RAG增强**: 基于向量检索的精准信息提取
5. **智能缓存**: 基于公司和年份的缓存机制
6. **资源管理**: 自动清理临时资源，避免存储浪费
7. **结构化输出**: 标准化的年报数据结构
8. **异常处理**: 完善的错误处理和资源清理机制
