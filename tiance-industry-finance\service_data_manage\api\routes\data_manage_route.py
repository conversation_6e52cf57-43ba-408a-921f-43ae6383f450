#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 12:30:00
# <AUTHOR> Assistant
# @File         : data_manage_route.py
# @Description  : 数据管理API路由
"""
import io
import re

import pandas as pd
from fastapi import APIRouter, UploadFile
from fastapi.responses import StreamingResponse
from fastapi import APIRouter, HTTPException, Body, Query, Response
from typing import Union, List, Optional

from pydantic import Field

from service_data_manage.entity.data_manage_entity import (
    CompanyDataStatsRequest,
    CompanyMainRequest,
    CompanyMainResponse,
    CompanyUpdateRequest,
    CompanyBatchUpdateRequest, CompanyDataDownloadRequest,
    CompanyBatchUpdateRequest,
    CompanyAddRequest,
    CompanyBatchAddRequest,
    CompanyDelRequest,
    CompanyBatchDelRequest
)
from entity.response_entity import SuccessResponse, FalseResponse
from service_data_manage.service.data_manage_service import (
    CompanyDataStatsService,
    CompanySearchService,
    CompanyUpdateService, CompanyDataUploadService, CompanyDataDownloadService,
    CompanyUpdateService,
    CompanyAddService,
    CompanyDelService,
    DuplicateCompanyError
)
from utils.log_util import LogUtil
from utils.ret_util import RetUtil
from utils.sql_util import SQLUtil

router = APIRouter()


@router.post("/company_data_stats", summary="公司数据总量查询")
async def company_data_stats(request: CompanyDataStatsRequest):
    """
    公司数据总量查询接口

    功能说明：
    1. 查询公司数据总量（status=1的记录总数）
    2. 查询上市公司总数（StockAbbr不为空的记录总数）
    3. 查询当日更新总数（update_time大于当日0点的记录总数）

    Args:
        request: 查询请求参数（当前无需参数）

    Returns:
        SuccessResponse: 包含统计数据的响应
        - total_count: 公司数据总量
        - listed_count: 上市公司总数
        - today_updated_count: 当日更新总数
        - query_date: 查询日期
    """
    try:
        # 记录请求日志
        LogUtil.log_json(describe="公司数据总量查询请求", kwargs=dict(request))

        # 调用服务层获取统计数据
        stats_data = CompanyDataStatsService.get_company_data_statistics()

        # 记录返回日志
        LogUtil.log_json(describe="公司数据总量查询返回结果", kwargs=stats_data)

        return RetUtil.response_ok(data=stats_data)

    except Exception as e:
        error_detail = f"公司数据总量查询失败：{str(e)}"
        LogUtil.error(msg=error_detail)

        error_data = {
            "error": error_detail,
            "error_type": "company_data_stats_error"
        }

        return RetUtil.response_error(data=error_data)


@router.post("/company_update", summary="公司数据修改（支持单个和批量）")
async def company_update(
    request: Union[CompanyUpdateRequest, CompanyBatchUpdateRequest, List[CompanyUpdateRequest]]):
    """
    公司数据修改接口（支持单个和批量）

    功能说明：
    1. 支持单个公司修改和批量公司修改
    2. 批量修改时，先检查所有数据，任何一个不符合就返回错误
    3. ChiName（中文名称）为必填项，不能为空
    4. ChiName修改后自动将原有值补充到PreName中
    5. PreName中的数据以逗号分割，自动去重
    6. 自动更新update_time
    7. 修改ChiName时会进行查重验证

    Args:
        request: 公司修改请求参数
        - 单个修改：CompanyUpdateRequest 对象
        - 批量修改：CompanyBatchUpdateRequest 对象或 List[CompanyUpdateRequest]

    Returns:
        SuccessResponse: 包含修改结果的响应
        - 单个修改：返回单个公司的修改结果
        - 批量修改：返回批量修改结果或第一个错误信息
    """
    try:
        # 判断是单个修改还是批量修改
        if isinstance(request, list):
            # 直接传入的列表
            companies_data = [
                {
                    "company_code": company.company_code,
                    "chi_name": company.chi_name,
                    "chi_name_abbr": company.chi_name_abbr,
                    "eng_name": company.eng_name
                }
                for company in request
            ]
            is_batch = True
            LogUtil.log_json(describe="公司数据批量修改请求（列表）", kwargs={"total_count": len(request)})
        elif hasattr(request, 'companies'):
            # CompanyBatchUpdateRequest 对象
            companies_data = [
                {
                    "company_code": company.company_code,
                    "chi_name": company.chi_name,
                    "chi_name_abbr": company.chi_name_abbr,
                    "eng_name": company.eng_name
                }
                for company in request.companies
            ]
            is_batch = True
            LogUtil.log_json(describe="公司数据批量修改请求", kwargs={"total_count": len(request.companies)})
        else:
            # 单个修改
            is_batch = False
            LogUtil.log_json(describe="公司数据修改请求", kwargs=dict(request))

        if is_batch:
            # 批量修改
            result = CompanyUpdateService.batch_update_companies(companies_data)
        else:
            # 单个修改
            result = CompanyUpdateService.update_company_info(
                company_code=request.company_code,
                chi_name=request.chi_name,
                chi_name_abbr=request.chi_name_abbr,
                eng_name=request.eng_name
            )

        # 记录返回日志
        LogUtil.log_json(describe="公司数据修改返回结果", kwargs=result)

        return RetUtil.response_ok(data=result)

    except ValueError as e:
        # 参数验证错误
        error_detail = f"参数验证失败：{str(e)}"
        LogUtil.error(msg=error_detail)

        error_data = {
            "error": error_detail,
            "error_type": "validation_error"
        }

        # 添加相关信息
        if hasattr(request, 'company_code'):
            error_data["company_code"] = request.company_code
        elif hasattr(request, 'companies'):
            error_data["total_count"] = len(request.companies)
        elif isinstance(request, list):
            error_data["total_count"] = len(request)

        return RetUtil.response_error(data=error_data)

    except Exception as e:
        # 其他错误
        error_detail = f"公司数据修改失败：{str(e)}"
        LogUtil.error(msg=error_detail)

        error_data = {
            "error": error_detail,
            "error_type": "company_update_error"
        }

        # 添加相关信息
        if hasattr(request, 'company_code'):
            error_data["company_code"] = request.company_code
        elif hasattr(request, 'companies'):
            error_data["total_count"] = len(request.companies)
        elif isinstance(request, list):
            error_data["total_count"] = len(request)

        return RetUtil.response_error(data=error_data)


@router.post("/company_data_upload", summary="从Excel表格导入新增公司数据到数据库")
async def company_data_upload(excel_file: UploadFile) -> Response:
    """
    公司数据导入接口 -- 导入字段均和数据库保持一致。
    导入要求CreditCode、ChiName必填。数据库其余字段选填。
    status（1）、creat_time（新增当日时间） 和update_time（新增当日时间）为默认，不暴露在接口入参中，
    新增数据需要对CreditCode、ChiName进行查重。

    :param excel_file: excel文件的字节流
    :return:
    """
    try:
        content = await excel_file.read()
        df = pd.read_excel(io.BytesIO(content))

        # 数据验证
        CompanyDataUploadService.validate(df)

        # 数据导入
        CompanyDataUploadService.upload(df)

        return RetUtil.response_ok(data={})
    except Exception as e:
        return RetUtil.response_error(message=f"数据导入失败：{str(e)}")


@router.post("/company_data_download", response_model=None, summary="根据公司编号列表，导出Excel文件")
async def company_data_download(request: CompanyDataDownloadRequest) -> StreamingResponse | Response:
    """
    根据公司编号列表，导出Excel文件
    :param request:
    :return: 文件数据流
    """

    try:
        file_stream = CompanyDataDownloadService.download(request.company_code_list)

        return StreamingResponse(file_stream,
                                 media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                 headers={"Content-Disposition": "attachment", "filename": "company_data.xlsx"})

    except Exception as e:
        return RetUtil.response_error(message=f"数据导出失败：{str(e)}")


@router.post("/company_add", summary="公司数据新增")
async def company_add(request: Union[CompanyAddRequest, CompanyBatchAddRequest, List[CompanyAddRequest]] = Body(...)):
    """
       公司数据新增接口

       功能说明：
       1. 支持三种输入形式：
           - 单个公司对象：AddCompanyMain
           - 批量添加（带字段名）：AddCompanyRequest（包含 company_list 字段）
           - 批量添加（裸列表）：List[AddCompanyMain]
       2. 会校验公司名称和统一社会信用代码是否重复
       3. 自动生成 CompanyCode，默认 status=1，并设置创建时间与更新时间

       Args:
           request: 公司新增请求体

       Returns:
           dict: 包含新增结果（成功数、失败数、详细信息）
       """

    try:
        # 请求体结构判断
        if isinstance(request, list):
            company_data_list = [CompanyAddService.convert_to_dict(c.dict()) for c in request]
            LogUtil.log_json(describe="公司数据批量新增请求（裸数组）", kwargs={"total_count": len(request)})

        elif hasattr(request, "company_list"):
            company_data_list = [CompanyAddService.convert_to_dict(c.dict()) for c in request.company_list]
            LogUtil.log_json(describe="公司数据批量新增请求（字段封装）",
                             kwargs={"total_count": len(request.company_list)})

        else:
            # 单个公司数据
            company_data_list = [CompanyAddService.convert_to_dict(request.dict())]
            LogUtil.log_json(describe="公司数据单个新增请求", kwargs=request.dict())

        add_results = CompanyAddService.add_companies(company_data_list)

        # 记录返回日志
        LogUtil.log_json(describe="公司数据新增返回结果", kwargs=add_results)

        return RetUtil.response_ok(data=add_results)

    except DuplicateCompanyError as e:
        # 处理数据重复异常
        LogUtil.warn(msg=f"公司数据新增失败: {e.detail}")
        error_data = {"error": e.detail, "error_type": "duplicate_company_error"}
        return RetUtil.response_error(data=error_data)

    except Exception as e:
        # 处理其他异常
        error_detail = f"公司数据新增失败：{str(e)}"
        LogUtil.error(msg=error_detail)
        error_data = {"error": error_detail, "error_type": "add_company_error"}
        return RetUtil.response_error(data=error_data)


@router.post("/company_del", summary="公司数据删除")
async def company_del(request: Union[CompanyDelRequest, CompanyBatchDelRequest, List[CompanyDelRequest]]):
    """
    公司数据删除接口

    功能说明：
    1. 支持三种输入形式：
        - 单个公司对象：CompanyDelRequest
        - 批量删除（带字段名）：CompanyBatchDelRequest（包含 company_list 字段）
        - 批量删除（裸列表）：List[CompanyDelRequest]
    2. 根据CompanyCode进行逻辑删除（将 status 设置为 0）
    3. 若公司不存在或已删除，返回失败信息

    Args:
        request: 公司删除请求体

    Returns:
        dict: 包含删除结果（总数、成功数、失败数、详细信息）
    """

    try:
        # 类型判断
        if isinstance(request, list):
            company_list = request
            LogUtil.log_json(describe="公司删除请求（裸列表）", kwargs={"total": len(request)})
        elif hasattr(request, "company_list"):
            company_list = request.company_list
            LogUtil.log_json(describe="公司删除请求（封装对象）", kwargs={"total": len(request.company_list)})
        else:
            company_list = [request]
            LogUtil.log_json(describe="公司删除请求（单个对象）", kwargs={"CreditCode": request.CreditCode})

        # 调用服务层执行删除操作
        delete_results = CompanyDelService.delete_companies(company_list)

        # 记录返回日志
        LogUtil.log_json(describe="公司数据删除返回结果", kwargs=delete_results)

        return RetUtil.response_ok(data=delete_results)


    except HTTPException as http_ex:
        error_data = {
            "error": http_ex.detail,
            "error_type": "http_error"
        }
        LogUtil.error(msg=http_ex.detail)
        return RetUtil.response_error(data=error_data)


    except Exception as e:
        error_detail = f"公司数据删除失败：{str(e)}"
        LogUtil.error(msg=error_detail)

        error_data = {
            "error": error_detail,
            "error_type": "company_delete_error"
        }

        return RetUtil.response_error(data=error_data)


@router.post("/company_search", summary="公司查询", response_model=List[CompanyMainResponse])
async def search_company(request: CompanyMainRequest) -> Response:
    """
    公司数据查询接口

    功能说明：
    1. 根据公司名称或进行模糊查询
    2. 根据公司统一社会信用代码进行精确查询
    3. 查询全部公司
    2. 支持分页查询
    

    Args:
        request: 公司查询请求体
        page_num 分页页码
        page_size 分页每页条数
        company_name_info 公司名称或统一社会信用代码
        company_name_info 输入公司名称 做模糊查询
        company_name_info 输入统一社会信用代码 做精确查询
        company_name_info 为空 查询全部公司
        

    Returns:
        JSONresponse: {"code":200, "status": True, "message": "success", "data": data}
    """
    try:
        db = SQLUtil.get_session()
        if request.company_name_info == "":
            result = CompanySearchService.query_all_company(db=db, page_num=request.page_num,
                                                            page_size=request.page_size)
        else:

            ## 判断传入的是否是creditcode
            pattern = r'^[A-Z0-9]+$'
            if bool(re.fullmatch(pattern, request.company_name_info)):
                result = CompanySearchService.query_company_by_creditcode(db=db, creditcode=request.company_name_info,
                                                                          page_num=request.page_num,
                                                                          page_size=request.page_size)
                # creditcode查询
            else:
                # 公司名称查询
                result = CompanySearchService.query_company_by_name(db=db, company_name=request.company_name_info,
                                                                    page_num=request.page_num,
                                                                    page_size=request.page_size)
        data = SQLUtil.models_to_list(result)
        return RetUtil.response_ok(data=data)

    except Exception as e:
        detail = f"服务器错误:{e}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)
